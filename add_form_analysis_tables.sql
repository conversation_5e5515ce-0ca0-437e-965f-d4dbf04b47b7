-- Check if tables already exist and create them if they don't

-- Create FormAnalysisStatus enum type if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'formanalysisstatus') THEN
        CREATE TYPE formanalysisstatus AS ENUM ('in_progress', 'completed', 'failed');
    END IF;
END$$;

-- Create form_analysis table if it doesn't exist
CREATE TABLE IF NOT EXISTS form_analysis (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    exercise_id INTEGER REFERENCES exercise_library(id),
    date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR,
    overall_score FLOAT,
    analysis_data JSONB,
    feedback TEXT,
    recommendations TEXT
);

-- Create form_issues table if it doesn't exist
CREATE TABLE IF NOT EXISTS form_issues (
    id SERIAL PRIMARY KEY,
    form_analysis_id INTEGER REFERENCES form_analysis(id) ON DELETE CASCADE,
    issue_type VARCHAR NOT NULL,
    severity FLOAT NOT NULL,
    frame_number INTEGER,
    description TEXT NOT NULL,
    recommendation TEXT
);

-- Create form_rules table if it doesn't exist
CREATE TABLE IF NOT EXISTS form_rules (
    id SERIAL PRIMARY KEY,
    exercise_id INTEGER REFERENCES exercise_library(id) ON DELETE CASCADE,
    knee_min_angle FLOAT,
    knee_max_angle FLOAT,
    hip_min_angle FLOAT,
    hip_max_angle FLOAT,
    back_min_angle FLOAT,
    back_max_angle FLOAT,
    knee_tracking_threshold FLOAT,
    bar_path_threshold FLOAT,
    additional_rules JSONB
);

-- Add unique constraint to exercise_id in form_rules if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'form_rules_exercise_id_key' 
        AND conrelid = 'form_rules'::regclass
    ) THEN
        ALTER TABLE form_rules ADD CONSTRAINT form_rules_exercise_id_key UNIQUE (exercise_id);
    END IF;
EXCEPTION
    WHEN undefined_table THEN
        -- Table doesn't exist yet, constraint will be added when table is created
        NULL;
END$$;

-- Add relationship to ExerciseLibrary if it doesn't exist
ALTER TABLE exercise_library ADD COLUMN IF NOT EXISTS form_rules_id INTEGER REFERENCES form_rules(id);
