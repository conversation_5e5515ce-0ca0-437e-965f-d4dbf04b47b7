                    conname                    | contype |        conrelid         |                                        pg_get_constraintdef                                        
-----------------------------------------------+---------+-------------------------+----------------------------------------------------------------------------------------------------
 pg_proc_oid_index                             | p       | pg_proc                 | PRIMARY KEY (oid)
 pg_proc_proname_args_nsp_index                | u       | pg_proc                 | UNIQUE (proname, proargtypes, pronamespace)
 pg_type_oid_index                             | p       | pg_type                 | PRIMARY KEY (oid)
 pg_type_typname_nsp_index                     | u       | pg_type                 | UNIQUE (typname, typnamespace)
 pg_attribute_relid_attnam_index               | u       | pg_attribute            | UNIQUE (attrelid, attname)
 pg_attribute_relid_attnum_index               | p       | pg_attribute            | PRIMARY KEY (attrelid, attnum)
 pg_class_oid_index                            | p       | pg_class                | PRIMARY KEY (oid)
 pg_class_relname_nsp_index                    | u       | pg_class                | UNIQUE (relname, relnamespace)
 pg_attrdef_adrelid_adnum_index                | u       | pg_attrdef              | UNIQUE (adrelid, adnum)
 pg_attrdef_oid_index                          | p       | pg_attrdef              | PRIMARY KEY (oid)
 pg_constraint_conrelid_contypid_conname_index | u       | pg_constraint           | UNIQUE (conrelid, contypid, conname)
 pg_constraint_oid_index                       | p       | pg_constraint           | PRIMARY KEY (oid)
 pg_inherits_relid_seqno_index                 | p       | pg_inherits             | PRIMARY KEY (inhrelid, inhseqno)
 pg_index_indexrelid_index                     | p       | pg_index                | PRIMARY KEY (indexrelid)
 pg_operator_oid_index                         | p       | pg_operator             | PRIMARY KEY (oid)
 pg_operator_oprname_l_r_n_index               | u       | pg_operator             | UNIQUE (oprname, oprleft, oprright, oprnamespace)
 pg_opfamily_am_name_nsp_index                 | u       | pg_opfamily             | UNIQUE (opfmethod, opfname, opfnamespace)
 pg_opfamily_oid_index                         | p       | pg_opfamily             | PRIMARY KEY (oid)
 pg_opclass_am_name_nsp_index                  | u       | pg_opclass              | UNIQUE (opcmethod, opcname, opcnamespace)
 pg_opclass_oid_index                          | p       | pg_opclass              | PRIMARY KEY (oid)
 pg_am_name_index                              | u       | pg_am                   | UNIQUE (amname)
 pg_am_oid_index                               | p       | pg_am                   | PRIMARY KEY (oid)
 pg_amop_fam_strat_index                       | u       | pg_amop                 | UNIQUE (amopfamily, amoplefttype, amoprighttype, amopstrategy)
 pg_amop_opr_fam_index                         | u       | pg_amop                 | UNIQUE (amopopr, amoppurpose, amopfamily)
 pg_amop_oid_index                             | p       | pg_amop                 | PRIMARY KEY (oid)
 pg_amproc_fam_proc_index                      | u       | pg_amproc               | UNIQUE (amprocfamily, amproclefttype, amprocrighttype, amprocnum)
 pg_amproc_oid_index                           | p       | pg_amproc               | PRIMARY KEY (oid)
 pg_language_name_index                        | u       | pg_language             | UNIQUE (lanname)
 pg_language_oid_index                         | p       | pg_language             | PRIMARY KEY (oid)
 pg_largeobject_metadata_oid_index             | p       | pg_largeobject_metadata | PRIMARY KEY (oid)
 pg_largeobject_loid_pn_index                  | p       | pg_largeobject          | PRIMARY KEY (loid, pageno)
 pg_aggregate_fnoid_index                      | p       | pg_aggregate            | PRIMARY KEY (aggfnoid)
 pg_statistic_relid_att_inh_index              | p       | pg_statistic            | PRIMARY KEY (starelid, staattnum, stainherit)
 pg_statistic_ext_oid_index                    | p       | pg_statistic_ext        | PRIMARY KEY (oid)
 pg_statistic_ext_name_index                   | u       | pg_statistic_ext        | UNIQUE (stxname, stxnamespace)
 pg_statistic_ext_data_stxoid_index            | p       | pg_statistic_ext_data   | PRIMARY KEY (stxoid)
 pg_rewrite_oid_index                          | p       | pg_rewrite              | PRIMARY KEY (oid)
 pg_rewrite_rel_rulename_index                 | u       | pg_rewrite              | UNIQUE (ev_class, rulename)
 pg_trigger_tgrelid_tgname_index               | u       | pg_trigger              | UNIQUE (tgrelid, tgname)
 pg_trigger_oid_index                          | p       | pg_trigger              | PRIMARY KEY (oid)
 pg_event_trigger_evtname_index                | u       | pg_event_trigger        | UNIQUE (evtname)
 pg_event_trigger_oid_index                    | p       | pg_event_trigger        | PRIMARY KEY (oid)
 pg_description_o_c_o_index                    | p       | pg_description          | PRIMARY KEY (objoid, classoid, objsubid)
 pg_cast_oid_index                             | p       | pg_cast                 | PRIMARY KEY (oid)
 pg_cast_source_target_index                   | u       | pg_cast                 | UNIQUE (castsource, casttarget)
 pg_enum_oid_index                             | p       | pg_enum                 | PRIMARY KEY (oid)
 pg_enum_typid_label_index                     | u       | pg_enum                 | UNIQUE (enumtypid, enumlabel)
 pg_enum_typid_sortorder_index                 | u       | pg_enum                 | UNIQUE (enumtypid, enumsortorder)
 pg_namespace_nspname_index                    | u       | pg_namespace            | UNIQUE (nspname)
 pg_namespace_oid_index                        | p       | pg_namespace            | PRIMARY KEY (oid)
 pg_conversion_default_index                   | u       | pg_conversion           | UNIQUE (connamespace, conforencoding, contoencoding, oid)
 pg_conversion_name_nsp_index                  | u       | pg_conversion           | UNIQUE (conname, connamespace)
 pg_conversion_oid_index                       | p       | pg_conversion           | PRIMARY KEY (oid)
 pg_database_datname_index                     | u       | pg_database             | UNIQUE (datname)
 pg_database_oid_index                         | p       | pg_database             | PRIMARY KEY (oid)
 pg_db_role_setting_databaseid_rol_index       | p       | pg_db_role_setting      | PRIMARY KEY (setdatabase, setrole)
 pg_tablespace_oid_index                       | p       | pg_tablespace           | PRIMARY KEY (oid)
 pg_tablespace_spcname_index                   | u       | pg_tablespace           | UNIQUE (spcname)
 pg_authid_rolname_index                       | u       | pg_authid               | UNIQUE (rolname)
 pg_authid_oid_index                           | p       | pg_authid               | PRIMARY KEY (oid)
 pg_auth_members_role_member_index             | p       | pg_auth_members         | PRIMARY KEY (roleid, member)
 pg_auth_members_member_role_index             | u       | pg_auth_members         | UNIQUE (member, roleid)
 pg_shdescription_o_c_index                    | p       | pg_shdescription        | PRIMARY KEY (objoid, classoid)
 pg_ts_config_cfgname_index                    | u       | pg_ts_config            | UNIQUE (cfgname, cfgnamespace)
 pg_ts_config_oid_index                        | p       | pg_ts_config            | PRIMARY KEY (oid)
 pg_ts_config_map_index                        | p       | pg_ts_config_map        | PRIMARY KEY (mapcfg, maptokentype, mapseqno)
 pg_ts_dict_dictname_index                     | u       | pg_ts_dict              | UNIQUE (dictname, dictnamespace)
 pg_ts_dict_oid_index                          | p       | pg_ts_dict              | PRIMARY KEY (oid)
 pg_ts_parser_prsname_index                    | u       | pg_ts_parser            | UNIQUE (prsname, prsnamespace)
 pg_ts_parser_oid_index                        | p       | pg_ts_parser            | PRIMARY KEY (oid)
 pg_ts_template_tmplname_index                 | u       | pg_ts_template          | UNIQUE (tmplname, tmplnamespace)
 pg_ts_template_oid_index                      | p       | pg_ts_template          | PRIMARY KEY (oid)
 pg_extension_oid_index                        | p       | pg_extension            | PRIMARY KEY (oid)
 pg_extension_name_index                       | u       | pg_extension            | UNIQUE (extname)
 pg_foreign_data_wrapper_oid_index             | p       | pg_foreign_data_wrapper | PRIMARY KEY (oid)
 pg_foreign_data_wrapper_name_index            | u       | pg_foreign_data_wrapper | UNIQUE (fdwname)
 pg_foreign_server_oid_index                   | p       | pg_foreign_server       | PRIMARY KEY (oid)
 pg_foreign_server_name_index                  | u       | pg_foreign_server       | UNIQUE (srvname)
 pg_user_mapping_oid_index                     | p       | pg_user_mapping         | PRIMARY KEY (oid)
 pg_user_mapping_user_server_index             | u       | pg_user_mapping         | UNIQUE (umuser, umserver)
 pg_foreign_table_relid_index                  | p       | pg_foreign_table        | PRIMARY KEY (ftrelid)
 pg_policy_oid_index                           | p       | pg_policy               | PRIMARY KEY (oid)
 pg_policy_polrelid_polname_index              | u       | pg_policy               | UNIQUE (polrelid, polname)
 pg_replication_origin_roiident_index          | p       | pg_replication_origin   | PRIMARY KEY (roident)
 pg_replication_origin_roname_index            | u       | pg_replication_origin   | UNIQUE (roname)
 pg_default_acl_role_nsp_obj_index             | u       | pg_default_acl          | UNIQUE (defaclrole, defaclnamespace, defaclobjtype)
 pg_default_acl_oid_index                      | p       | pg_default_acl          | PRIMARY KEY (oid)
 pg_init_privs_o_c_o_index                     | p       | pg_init_privs           | PRIMARY KEY (objoid, classoid, objsubid)
 pg_seclabel_object_index                      | p       | pg_seclabel             | PRIMARY KEY (objoid, classoid, objsubid, provider)
 pg_shseclabel_object_index                    | p       | pg_shseclabel           | PRIMARY KEY (objoid, classoid, provider)
 pg_collation_name_enc_nsp_index               | u       | pg_collation            | UNIQUE (collname, collencoding, collnamespace)
 pg_collation_oid_index                        | p       | pg_collation            | PRIMARY KEY (oid)
 pg_partitioned_table_partrelid_index          | p       | pg_partitioned_table    | PRIMARY KEY (partrelid)
 pg_range_rngtypid_index                       | p       | pg_range                | PRIMARY KEY (rngtypid)
 pg_range_rngmultitypid_index                  | u       | pg_range                | UNIQUE (rngmultitypid)
 pg_transform_oid_index                        | p       | pg_transform            | PRIMARY KEY (oid)
 pg_transform_type_lang_index                  | u       | pg_transform            | UNIQUE (trftype, trflang)
 pg_sequence_seqrelid_index                    | p       | pg_sequence             | PRIMARY KEY (seqrelid)
 pg_publication_oid_index                      | p       | pg_publication          | PRIMARY KEY (oid)
 pg_publication_pubname_index                  | u       | pg_publication          | UNIQUE (pubname)
 pg_publication_rel_oid_index                  | p       | pg_publication_rel      | PRIMARY KEY (oid)
 pg_publication_rel_prrelid_prpubid_index      | u       | pg_publication_rel      | UNIQUE (prrelid, prpubid)
 pg_subscription_oid_index                     | p       | pg_subscription         | PRIMARY KEY (oid)
 pg_subscription_subname_index                 | u       | pg_subscription         | UNIQUE (subdbid, subname)
 pg_subscription_rel_srrelid_srsubid_index     | p       | pg_subscription_rel     | PRIMARY KEY (srrelid, srsubid)
 cardinal_number_domain_check                  | c       | -                       | CHECK ((VALUE >= 0))
 yes_or_no_check                               | c       | -                       | CHECK (((VALUE)::text = ANY ((ARRAY['YES'::character varying, 'NO'::character varying])::text[])))
 users_pkey                                    | p       | users                   | PRIMARY KEY (id)
 workouts_pkey                                 | p       | workouts                | PRIMARY KEY (id)
 workouts_user_id_fkey                         | f       | workouts                | FOREIGN KEY (user_id) REFERENCES users(id)
 exercises_pkey                                | p       | exercises               | PRIMARY KEY (id)
 exercises_workout_id_fkey                     | f       | exercises               | FOREIGN KEY (workout_id) REFERENCES workouts(id) ON DELETE CASCADE
 exercise_sets_pkey                            | p       | exercise_sets           | PRIMARY KEY (id)
 exercise_sets_exercise_id_fkey                | f       | exercise_sets           | FOREIGN KEY (exercise_id) REFERENCES exercises(id) ON DELETE CASCADE
 muscle_groups_pkey                            | p       | muscle_groups           | PRIMARY KEY (id)
 muscle_groups_name_key                        | u       | muscle_groups           | UNIQUE (name)
 equipment_pkey                                | p       | equipment               | PRIMARY KEY (id)
 equipment_name_key                            | u       | equipment               | UNIQUE (name)
 exercise_library_pkey                         | p       | exercise_library        | PRIMARY KEY (id)
 exercise_muscle_groups_pkey                   | p       | exercise_muscle_groups  | PRIMARY KEY (exercise_id, muscle_group_id)
 exercise_muscle_groups_exercise_id_fkey       | f       | exercise_muscle_groups  | FOREIGN KEY (exercise_id) REFERENCES exercise_library(id) ON DELETE CASCADE
 exercise_muscle_groups_muscle_group_id_fkey   | f       | exercise_muscle_groups  | FOREIGN KEY (muscle_group_id) REFERENCES muscle_groups(id) ON DELETE CASCADE
 exercise_equipment_pkey                       | p       | exercise_equipment      | PRIMARY KEY (exercise_id, equipment_id)
 exercise_equipment_exercise_id_fkey           | f       | exercise_equipment      | FOREIGN KEY (exercise_id) REFERENCES exercise_library(id) ON DELETE CASCADE
 exercise_equipment_equipment_id_fkey          | f       | exercise_equipment      | FOREIGN KEY (equipment_id) REFERENCES equipment(id) ON DELETE CASCADE
 alembic_version_pkc                           | p       | alembic_version         | PRIMARY KEY (version_num)
 form_analysis_pkey                            | p       | form_analysis           | PRIMARY KEY (id)
 form_analysis_user_id_fkey                    | f       | form_analysis           | FOREIGN KEY (user_id) REFERENCES users(id)
 form_analysis_exercise_id_fkey                | f       | form_analysis           | FOREIGN KEY (exercise_id) REFERENCES exercise_library(id)
 form_rules_pkey                               | p       | form_rules              | PRIMARY KEY (id)
 form_rules_exercise_id_key                    | u       | form_rules              | UNIQUE (exercise_id)
 form_rules_exercise_id_fkey                   | f       | form_rules              | FOREIGN KEY (exercise_id) REFERENCES exercise_library(id) ON DELETE CASCADE
 form_issues_pkey                              | p       | form_issues             | PRIMARY KEY (id)
 form_issues_form_analysis_id_fkey             | f       | form_issues             | FOREIGN KEY (form_analysis_id) REFERENCES form_analysis(id) ON DELETE CASCADE
(134 rows)

