version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - workout-network
    environment:
      - TZ=America/New_York  # Set timezone to Eastern Time
    volumes:
      - ./frontend/nginx.conf:/etc/nginx/conf.d/default.conf
    # Don't mount source code as volume to ensure we use the built version

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
    environment:
      - DATABASE_URL=********************************************
      - SECRET_KEY=${SECRET_KEY:-your_very_secure_secret_key_change_in_production}
      - POSTGRES_HOST=db
      - POSTGRES_PORT=5432
      - POSTGRES_DB=workout_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - TZ=America/New_York  # Set timezone to Eastern Time
 #     - MOCK_ANALYSIS=false  # Add this line to enable mock analysis
    networks:
      - workout-network
    volumes:
      - ./backend:/app

  db:
    image: postgres:14
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_HOST=db
      - POSTGRES_PORT=5432
      - POSTGRES_DB=workout_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - TZ=America/New_York  # Set timezone to Eastern Time
    networks:
      - workout-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    ports:
      - "5432:5432"

networks:
  workout-network:
    driver: bridge

volumes:
  postgres_data: