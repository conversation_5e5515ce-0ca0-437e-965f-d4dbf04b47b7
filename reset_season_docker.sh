#!/bin/bash

# Docker wrapper script for season reset
# This script runs the season reset from within the Docker container

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    DOCKER_COMPOSE_CMD="docker compose"
else
    DOCKER_COMPOSE_CMD="docker-compose"
fi

# Find the backend container
print_status "Looking for backend container..."
BACKEND_CONTAINER=$($DOCKER_COMPOSE_CMD ps -q backend 2>/dev/null)

if [ -z "$BACKEND_CONTAINER" ]; then
    print_error "Backend container not found. Make sure the containers are running with:"
    echo "  $DOCKER_COMPOSE_CMD up -d"
    exit 1
fi

print_success "Found backend container: $BACKEND_CONTAINER"

# Parse command line arguments
CONFIRM_FLAG=""
if [ "$1" = "--confirm" ]; then
    CONFIRM_FLAG="--confirm"
    print_warning "Running in auto-confirm mode (no prompts)"
fi

echo ""
print_status "Starting season reset process..."
print_warning "This will reset ALL points, achievements, and scoring data!"
print_warning "User accounts, workouts, and exercise data will be preserved."

if [ -z "$CONFIRM_FLAG" ]; then
    echo ""
    read -p "Are you sure you want to proceed? (yes/no): " confirm
    
    if [ "$confirm" != "yes" ]; then
        print_status "Season reset cancelled by user"
        exit 0
    fi
fi

echo ""
print_status "Executing season reset in Docker container..."

# Execute the Python script in the container
if $DOCKER_COMPOSE_CMD exec backend python reset_season.py $CONFIRM_FLAG; then
    echo ""
    print_success "Season reset completed successfully!"
    echo ""
    print_status "Next steps to reinitialize scoring system:"
    print_status "1. Initialize achievements and milestones:"
    echo "   $DOCKER_COMPOSE_CMD exec backend python seed_scoring.py"
    print_status "2. Calculate scores for existing workouts:"
    echo "   $DOCKER_COMPOSE_CMD exec backend python calculate_existing_scores.py"
    echo ""
    print_status "Or run both steps automatically:"
    echo "   ./reset_season_docker.sh --confirm && $DOCKER_COMPOSE_CMD exec backend python seed_scoring.py && $DOCKER_COMPOSE_CMD exec backend python calculate_existing_scores.py"
else
    print_error "Season reset failed. Check the error messages above."
    exit 1
fi

print_success "Done!"
