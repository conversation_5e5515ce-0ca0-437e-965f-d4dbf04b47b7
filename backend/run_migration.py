import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database connection parameters from environment variables
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", "5432")
DB_NAME = os.getenv("DB_NAME", "workout_tracker")
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASSWORD = os.getenv("DB_PASSWORD", "postgres")

# Connect to the database
conn = psycopg2.connect(
    host=DB_HOST,
    port=DB_PORT,
    dbname=DB_NAME,
    user=DB_USER,
    password=DB_PASSWORD
)

# Create a cursor
cur = conn.cursor()

# Read and execute the migration SQL
with open('migrations/create_exercise_library.sql', 'r') as f:
    migration_sql = f.read()
    cur.execute(migration_sql)

# Commit the changes
conn.commit()

# Close the cursor and connection
cur.close()
conn.close()

print("Migration completed successfully!")
