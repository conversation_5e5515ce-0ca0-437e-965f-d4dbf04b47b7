#!/usr/bin/env python3
# backend/add_user_points.py
import sys
import os
import logging
import argparse
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import models and database
from database import SessionLocal, engine
import models

def add_points_to_user(username, points_to_add):
    """
    Add points to a specific user's total score
    
    Args:
        username (str): The username of the user to add points to
        points_to_add (int): The number of points to add
        
    Returns:
        bool: True if successful, False otherwise
    """
    db = SessionLocal()
    try:
        # Find the user by username
        user = db.query(models.User).filter(models.User.username == username).first()
        
        if not user:
            logger.error(f"User '{username}' not found")
            return False
            
        # Get user stats
        user_stats = db.query(models.UserStat).filter(models.UserStat.user_id == user.id).first()
        
        if not user_stats:
            # Create user stats if they don't exist
            logger.info(f"Creating stats for user {username} (ID: {user.id})")
            user_stats = models.UserStat(
                user_id=user.id,
                total_score=0,
                current_streak=0,
                longest_streak=0,
                total_workouts=0,
                unique_exercises=0,
                last_updated=datetime.utcnow()
            )
            db.add(user_stats)
            db.flush()
        
        # Get current score
        current_score = user_stats.total_score
        
        # Add points
        user_stats.total_score += points_to_add
        user_stats.last_updated = datetime.utcnow()
        
        # Commit changes
        db.commit()
        
        logger.info(f"Successfully added {points_to_add} points to user '{username}'")
        logger.info(f"Previous score: {current_score}, New score: {user_stats.total_score}")
        
        return True
    except Exception as e:
        db.rollback()
        logger.error(f"Error adding points to user: {str(e)}")
        return False
    finally:
        db.close()

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Add points to a user')
    parser.add_argument('username', type=str, help='Username of the user to add points to')
    parser.add_argument('points', type=int, help='Number of points to add')
    
    args = parser.parse_args()
    
    # Add points to the user
    success = add_points_to_user(args.username, args.points)
    
    if success:
        logger.info("Points added successfully")
        sys.exit(0)
    else:
        logger.error("Failed to add points")
        sys.exit(1)

if __name__ == "__main__":
    main()
