# backend/form_analysis_service.py
import os
import logging
import cv2
import numpy as np
import mediapipe as mp
import math
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import models
from sqlalchemy.orm import Session

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)8s | %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FormAnalysisService:
    """Service for analyzing exercise form using MediaPipe pose detection"""
    
    def __init__(self):
        """Initialize the form analysis service"""
        logger.info("Initializing Form Analysis Service")
        
        # Initialize MediaPipe pose detection
        self.mp_pose = mp.solutions.pose
        self.mp_drawing = mp.solutions.drawing_utils
        self.mp_drawing_styles = mp.solutions.drawing_styles
        
        # Initialize pose detector with high accuracy settings
        self.pose = self.mp_pose.Pose(
            static_image_mode=False,
            model_complexity=2,  # 0, 1, or 2. Higher is more accurate but slower
            smooth_landmarks=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        
        # Barbell back squat specific rules
        self.squat_rules = {
            "knee_min_angle": 80,  # Minimum knee angle at bottom position (degrees)
            "knee_max_angle": 170,  # Maximum knee angle at top position (degrees)
            "hip_min_angle": 50,   # Minimum hip angle at bottom position (degrees)
            "hip_max_angle": 170,  # Maximum hip angle at top position (degrees)
            "back_min_angle": 45,  # Minimum back angle relative to ground (degrees)
            "back_max_angle": 90,  # Maximum back angle relative to ground (degrees)
            "knee_tracking_threshold": 15,  # Maximum knee deviation from ankle (degrees)
            "depth_threshold": 0.95,  # Hip should be below knee (ratio of hip to knee height)
        }
        
        logger.info("✅ Form Analysis Service initialized")
    
    def process_video(self, video_path: str, user_id: int, exercise_id: int, db: Session) -> Dict[str, Any]:
        """
        Process a video file for form analysis
        
        Args:
            video_path: Path to the video file
            user_id: ID of the user
            exercise_id: ID of the exercise being performed
            db: Database session
            
        Returns:
            Analysis results
        """
        logger.info(f"Processing video for form analysis: {video_path}")
        
        # Create a new form analysis record
        form_analysis = models.FormAnalysis(
            user_id=user_id,
            exercise_id=exercise_id,
            status=models.FormAnalysisStatus.in_progress
        )
        db.add(form_analysis)
        db.commit()
        db.refresh(form_analysis)
        
        try:
            # Open the video file
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise ValueError(f"Could not open video file: {video_path}")
            
            # Get video properties
            frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            logger.info(f"Video properties: {frame_width}x{frame_height}, {fps} FPS, {total_frames} frames")
            
            # Initialize analysis data
            analysis_data = {
                "frames_processed": 0,
                "keypoints": [],
                "angles": {
                    "knee": [],
                    "hip": [],
                    "back": [],
                },
                "issues": [],
                "squat_depth": [],
                "knee_tracking": [],
                "bar_path": []
            }
            
            # Process frames
            frame_count = 0
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Process every 3rd frame to reduce computation (adjust as needed)
                if frame_count % 3 == 0:
                    # Process the frame
                    results = self._process_frame(frame)
                    if results:
                        # Extract keypoints and calculate angles
                        keypoints = self._extract_keypoints(results)
                        angles = self._calculate_angles(keypoints)
                        
                        # Check for form issues
                        issues = self._check_form_issues(keypoints, angles, frame_count)
                        
                        # Store data
                        analysis_data["keypoints"].append(keypoints)
                        analysis_data["angles"]["knee"].append(angles["knee"])
                        analysis_data["angles"]["hip"].append(angles["hip"])
                        analysis_data["angles"]["back"].append(angles["back"])
                        
                        # Store squat depth
                        if "depth_ratio" in angles:
                            analysis_data["squat_depth"].append(angles["depth_ratio"])
                        
                        # Store knee tracking
                        if "knee_tracking" in angles:
                            analysis_data["knee_tracking"].append(angles["knee_tracking"])
                        
                        # Store bar path
                        if "bar_position" in keypoints:
                            analysis_data["bar_path"].append(keypoints["bar_position"])
                        
                        # Add issues to the database and analysis data
                        for issue in issues:
                            # Add to database
                            db_issue = models.FormIssue(
                                form_analysis_id=form_analysis.id,
                                issue_type=issue["type"],
                                severity=issue["severity"],
                                frame_number=frame_count,
                                description=issue["description"],
                                recommendation=issue["recommendation"]
                            )
                            db.add(db_issue)
                            
                            # Add to analysis data
                            analysis_data["issues"].append(issue)
                
                frame_count += 1
                analysis_data["frames_processed"] += 1
            
            # Calculate overall score and generate feedback
            score, feedback, recommendations = self._generate_feedback(analysis_data)
            
            # Update the form analysis record
            form_analysis.status = models.FormAnalysisStatus.completed
            form_analysis.overall_score = score
            form_analysis.analysis_data = analysis_data
            form_analysis.feedback = feedback
            form_analysis.recommendations = recommendations
            
            db.commit()
            
            # Clean up
            cap.release()
            
            logger.info(f"✅ Video processing completed: {score:.2f}/10 score")
            
            return {
                "id": form_analysis.id,
                "overall_score": score,
                "feedback": feedback,
                "recommendations": recommendations,
                "analysis_data": analysis_data
            }
            
        except Exception as e:
            logger.error(f"❌ Error processing video: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
            # Update the form analysis record
            form_analysis.status = models.FormAnalysisStatus.failed
            form_analysis.feedback = f"Error processing video: {str(e)}"
            db.commit()
            
            return {
                "id": form_analysis.id,
                "error": str(e),
                "status": "failed"
            }
    
    def _process_frame(self, frame):
        """Process a single frame with MediaPipe pose detection"""
        # Convert BGR to RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Process the frame
        results = self.pose.process(frame_rgb)
        
        return results
    
    def _extract_keypoints(self, results):
        """Extract relevant keypoints from MediaPipe pose detection results"""
        if not results.pose_landmarks:
            return None
        
        landmarks = results.pose_landmarks.landmark
        
        # Extract key points for squat analysis
        keypoints = {
            # Left side
            "left_shoulder": (landmarks[self.mp_pose.PoseLandmark.LEFT_SHOULDER.value].x,
                             landmarks[self.mp_pose.PoseLandmark.LEFT_SHOULDER.value].y),
            "left_hip": (landmarks[self.mp_pose.PoseLandmark.LEFT_HIP.value].x,
                        landmarks[self.mp_pose.PoseLandmark.LEFT_HIP.value].y),
            "left_knee": (landmarks[self.mp_pose.PoseLandmark.LEFT_KNEE.value].x,
                         landmarks[self.mp_pose.PoseLandmark.LEFT_KNEE.value].y),
            "left_ankle": (landmarks[self.mp_pose.PoseLandmark.LEFT_ANKLE.value].x,
                          landmarks[self.mp_pose.PoseLandmark.LEFT_ANKLE.value].y),
            
            # Right side
            "right_shoulder": (landmarks[self.mp_pose.PoseLandmark.RIGHT_SHOULDER.value].x,
                              landmarks[self.mp_pose.PoseLandmark.RIGHT_SHOULDER.value].y),
            "right_hip": (landmarks[self.mp_pose.PoseLandmark.RIGHT_HIP.value].x,
                         landmarks[self.mp_pose.PoseLandmark.RIGHT_HIP.value].y),
            "right_knee": (landmarks[self.mp_pose.PoseLandmark.RIGHT_KNEE.value].x,
                          landmarks[self.mp_pose.PoseLandmark.RIGHT_KNEE.value].y),
            "right_ankle": (landmarks[self.mp_pose.PoseLandmark.RIGHT_ANKLE.value].x,
                           landmarks[self.mp_pose.PoseLandmark.RIGHT_ANKLE.value].y),
        }
        
        # Estimate bar position (between shoulders)
        keypoints["bar_position"] = (
            (keypoints["left_shoulder"][0] + keypoints["right_shoulder"][0]) / 2,
            (keypoints["left_shoulder"][1] + keypoints["right_shoulder"][1]) / 2 - 0.05  # Slightly above shoulders
        )
        
        return keypoints
    
    def _calculate_angles(self, keypoints):
        """Calculate joint angles from keypoints"""
        if not keypoints:
            return {}
        
        # Calculate knee angles (average of left and right)
        left_knee_angle = self._calculate_angle(
            keypoints["left_hip"], 
            keypoints["left_knee"], 
            keypoints["left_ankle"]
        )
        
        right_knee_angle = self._calculate_angle(
            keypoints["right_hip"], 
            keypoints["right_knee"], 
            keypoints["right_ankle"]
        )
        
        knee_angle = (left_knee_angle + right_knee_angle) / 2
        
        # Calculate hip angles (average of left and right)
        left_hip_angle = self._calculate_angle(
            keypoints["left_shoulder"], 
            keypoints["left_hip"], 
            keypoints["left_knee"]
        )
        
        right_hip_angle = self._calculate_angle(
            keypoints["right_shoulder"], 
            keypoints["right_hip"], 
            keypoints["right_knee"]
        )
        
        hip_angle = (left_hip_angle + right_hip_angle) / 2
        
        # Calculate back angle (relative to vertical)
        # Average the left and right side
        left_back_vector = (
            keypoints["left_shoulder"][0] - keypoints["left_hip"][0],
            keypoints["left_shoulder"][1] - keypoints["left_hip"][1]
        )
        
        right_back_vector = (
            keypoints["right_shoulder"][0] - keypoints["right_hip"][0],
            keypoints["right_shoulder"][1] - keypoints["right_hip"][1]
        )
        
        # Average the vectors
        back_vector = (
            (left_back_vector[0] + right_back_vector[0]) / 2,
            (left_back_vector[1] + right_back_vector[1]) / 2
        )
        
        # Calculate angle with vertical (y-axis)
        back_angle = math.degrees(math.atan2(abs(back_vector[0]), abs(back_vector[1])))
        
        # Calculate squat depth (hip height relative to knee height)
        left_hip_height = keypoints["left_hip"][1]
        left_knee_height = keypoints["left_knee"][1]
        right_hip_height = keypoints["right_hip"][1]
        right_knee_height = keypoints["right_knee"][1]
        
        # Average the depth ratios (higher ratio means deeper squat)
        # Note: y-coordinates increase downward in image space
        left_depth_ratio = (left_hip_height - left_knee_height) / abs(left_knee_height)
        right_depth_ratio = (right_hip_height - right_knee_height) / abs(right_knee_height)
        depth_ratio = (left_depth_ratio + right_depth_ratio) / 2
        
        # Calculate knee tracking (knees should track over toes)
        # Measure horizontal deviation of knee from ankle
        left_knee_tracking = abs(keypoints["left_knee"][0] - keypoints["left_ankle"][0])
        right_knee_tracking = abs(keypoints["right_knee"][0] - keypoints["right_ankle"][0])
        knee_tracking = (left_knee_tracking + right_knee_tracking) / 2
        
        return {
            "knee": knee_angle,
            "hip": hip_angle,
            "back": back_angle,
            "depth_ratio": depth_ratio,
            "knee_tracking": knee_tracking
        }
    
    def _calculate_angle(self, a, b, c):
        """Calculate the angle between three points (in degrees)"""
        # Convert to numpy arrays for vector operations
        a = np.array(a)
        b = np.array(b)
        c = np.array(c)
        
        # Calculate vectors
        ba = a - b
        bc = c - b
        
        # Calculate angle using dot product
        cosine_angle = np.dot(ba, bc) / (np.linalg.norm(ba) * np.linalg.norm(bc))
        angle = np.arccos(np.clip(cosine_angle, -1.0, 1.0))
        
        # Convert to degrees
        return math.degrees(angle)
    
    def _check_form_issues(self, keypoints, angles, frame_number):
        """Check for form issues based on calculated angles and positions"""
        issues = []
        
        # Check knee angle (should be at least 90 degrees at bottom)
        if angles["knee"] < self.squat_rules["knee_min_angle"]:
            issues.append({
                "type": "knee_angle_too_small",
                "severity": 0.7,
                "frame_number": frame_number,
                "description": f"Knee angle too small ({angles['knee']:.1f}°). This puts excessive stress on the knees.",
                "recommendation": "Try to maintain at least 80-90° knee angle at the bottom of the squat."
            })
        
        # Check hip angle (should be at least 50 degrees at bottom)
        if angles["hip"] < self.squat_rules["hip_min_angle"]:
            issues.append({
                "type": "hip_angle_too_small",
                "severity": 0.6,
                "frame_number": frame_number,
                "description": f"Hip angle too small ({angles['hip']:.1f}°). This may indicate excessive forward lean.",
                "recommendation": "Focus on sitting back more and keeping your chest up."
            })
        
        # Check back angle (should be at least 45 degrees relative to ground)
        if angles["back"] < self.squat_rules["back_min_angle"]:
            issues.append({
                "type": "excessive_forward_lean",
                "severity": 0.8,
                "frame_number": frame_number,
                "description": f"Excessive forward lean detected. Back angle is {angles['back']:.1f}°.",
                "recommendation": "Keep your chest up and core tight. Focus on a more upright torso position."
            })
        
        # Check squat depth (hip should be below knee)
        if angles["depth_ratio"] < self.squat_rules["depth_threshold"]:
            issues.append({
                "type": "insufficient_depth",
                "severity": 0.5,
                "frame_number": frame_number,
                "description": "Insufficient squat depth. Hip is not below knee level.",
                "recommendation": "Try to squat deeper, with hip crease going below knee level for a full range of motion."
            })
        
        # Check knee tracking (knees should track over toes)
        if angles["knee_tracking"] > self.squat_rules["knee_tracking_threshold"]:
            issues.append({
                "type": "knee_valgus",
                "severity": 0.9,
                "frame_number": frame_number,
                "description": "Knees caving inward (valgus collapse).",
                "recommendation": "Focus on pushing knees outward in line with toes. Consider using a resistance band around knees during warm-up."
            })
        
        return issues
    
    def _generate_feedback(self, analysis_data):
        """Generate overall feedback and recommendations based on analysis data"""
        # Count issues by type
        issue_counts = {}
        for issue in analysis_data["issues"]:
            issue_type = issue["type"]
            if issue_type in issue_counts:
                issue_counts[issue_type] += 1
            else:
                issue_counts[issue_type] = 1
        
        # Calculate average angles
        avg_knee_angle = sum(analysis_data["angles"]["knee"]) / len(analysis_data["angles"]["knee"]) if analysis_data["angles"]["knee"] else 0
        avg_hip_angle = sum(analysis_data["angles"]["hip"]) / len(analysis_data["angles"]["hip"]) if analysis_data["angles"]["hip"] else 0
        avg_back_angle = sum(analysis_data["angles"]["back"]) / len(analysis_data["angles"]["back"]) if analysis_data["angles"]["back"] else 0
        
        # Calculate min angles (at bottom of squat)
        min_knee_angle = min(analysis_data["angles"]["knee"]) if analysis_data["angles"]["knee"] else 0
        min_hip_angle = min(analysis_data["angles"]["hip"]) if analysis_data["angles"]["hip"] else 0
        
        # Calculate max depth
        max_depth = max(analysis_data["squat_depth"]) if analysis_data["squat_depth"] else 0
        
        # Generate score (0-10)
        # Start with perfect score and deduct based on issues
        score = 10.0
        
        # Deduct for each type of issue
        if "knee_angle_too_small" in issue_counts:
            score -= 1.5 * min(1.0, issue_counts["knee_angle_too_small"] / 10)
        
        if "hip_angle_too_small" in issue_counts:
            score -= 1.0 * min(1.0, issue_counts["hip_angle_too_small"] / 10)
        
        if "excessive_forward_lean" in issue_counts:
            score -= 2.0 * min(1.0, issue_counts["excessive_forward_lean"] / 10)
        
        if "insufficient_depth" in issue_counts:
            score -= 1.5 * min(1.0, issue_counts["insufficient_depth"] / 10)
        
        if "knee_valgus" in issue_counts:
            score -= 2.5 * min(1.0, issue_counts["knee_valgus"] / 10)
        
        # Ensure score is between 0 and 10
        score = max(0, min(10, score))
        
        # Generate feedback
        feedback_parts = []
        
        # Overall assessment
        if score >= 9:
            feedback_parts.append("Excellent squat form! Your technique is very good.")
        elif score >= 7:
            feedback_parts.append("Good squat form with some minor issues to address.")
        elif score >= 5:
            feedback_parts.append("Average squat form with several issues that need attention.")
        else:
            feedback_parts.append("Your squat form needs significant improvement to prevent injury and maximize effectiveness.")
        
        # Add specific feedback
        feedback_parts.append(f"Minimum knee angle: {min_knee_angle:.1f}° (recommended: ≥ 80°)")
        feedback_parts.append(f"Minimum hip angle: {min_hip_angle:.1f}° (recommended: ≥ 50°)")
        feedback_parts.append(f"Average back angle: {avg_back_angle:.1f}° (recommended: 45-90°)")
        
        if max_depth >= self.squat_rules["depth_threshold"]:
            feedback_parts.append("You achieved proper squat depth with hip below knee level.")
        else:
            feedback_parts.append("You did not consistently reach proper squat depth (hip below knee level).")
        
        # Generate recommendations
        recommendations_parts = []
        
        if "knee_angle_too_small" in issue_counts:
            recommendations_parts.append("Work on maintaining at least 80-90° knee angle at the bottom of the squat to reduce stress on the knees.")
        
        if "hip_angle_too_small" in issue_counts:
            recommendations_parts.append("Practice sitting back more during the squat to maintain a better hip angle.")
        
        if "excessive_forward_lean" in issue_counts:
            recommendations_parts.append("Focus on keeping your chest up and core tight to maintain a more upright torso position.")
        
        if "insufficient_depth" in issue_counts:
            recommendations_parts.append("Work on mobility to achieve proper squat depth with hip crease below knee level.")
        
        if "knee_valgus" in issue_counts:
            recommendations_parts.append("Practice keeping knees tracking in line with toes. Consider using resistance bands around knees during warm-up sets.")
        
        # Add general recommendations
        recommendations_parts.append("Record your squats regularly from side and front angles to monitor your form.")
        recommendations_parts.append("Consider working with a coach to address specific form issues.")
        
        # Join feedback and recommendations
        feedback = "\n".join(feedback_parts)
        recommendations = "\n".join(recommendations_parts)
        
        return score, feedback, recommendations
