# backend/utils/timezone_utils.py
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

def get_eastern_now():
    """
    Get the current datetime using the system timezone
    
    The system timezone is set to Eastern Time (America/New_York) in the Docker container
    via the TZ environment variable in docker-compose.yml
    
    Returns:
        datetime: Current datetime in the system timezone (Eastern Time)
    """
    # Simply use datetime.now() which will use the system timezone
    # The system timezone is set to Eastern Time in the Docker container
    now = datetime.now()
    
    # Log the current time for debugging
    logger.info(f"Current system time: {now}")
    
    return now
