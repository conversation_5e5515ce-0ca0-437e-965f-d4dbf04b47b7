"""
Utility functions for getting and formatting exercise lists from the database.
"""
from sqlalchemy.orm import Session
import models
from typing import List, Dict, Optional

def get_all_exercises(db: Session) -> List[models.ExerciseLibrary]:
    """
    Get all exercises from the exercise library.
    
    Args:
        db: Database session
        
    Returns:
        List of all exercises in the library
    """
    return db.query(models.ExerciseLibrary).all()

def get_exercises_by_equipment(db: Session, equipment_names: List[str]) -> List[models.ExerciseLibrary]:
    """
    Get exercises that can be performed with the specified equipment.
    
    Args:
        db: Database session
        equipment_names: List of equipment names
        
    Returns:
        List of exercises that can be performed with the specified equipment
    """
    if not equipment_names:
        # If no equipment specified, return bodyweight exercises
        return db.query(models.ExerciseLibrary).filter(
            ~models.ExerciseLibrary.equipment.any()
        ).all()
    
    # Get equipment IDs
    equipment_ids = db.query(models.Equipment.id).filter(
        models.Equipment.name.in_(equipment_names)
    ).all()
    equipment_ids = [eq[0] for eq in equipment_ids]
    
    # Get exercises that use any of the specified equipment
    exercises = db.query(models.ExerciseLibrary).filter(
        models.ExerciseLibrary.equipment.any(models.Equipment.id.in_(equipment_ids))
    ).all()
    
    # Also include bodyweight exercises
    bodyweight_exercises = db.query(models.ExerciseLibrary).filter(
        ~models.ExerciseLibrary.equipment.any()
    ).all()
    
    # Combine and remove duplicates
    all_exercises = list(set(exercises + bodyweight_exercises))
    
    return all_exercises

def format_exercises_for_llm(exercises: List[models.ExerciseLibrary]) -> str:
    """
    Format exercises for the LLM prompt.
    
    Args:
        exercises: List of exercises
        
    Returns:
        Formatted string of exercises
    """
    # Group exercises by type
    exercises_by_type = {}
    for exercise in exercises:
        if exercise.exercise_type not in exercises_by_type:
            exercises_by_type[exercise.exercise_type] = []
        exercises_by_type[exercise.exercise_type].append(exercise)
    
    # Format the output
    output = []
    for exercise_type, exercise_list in exercises_by_type.items():
        output.append(f"## {exercise_type.capitalize()} Exercises:")
        for exercise in sorted(exercise_list, key=lambda x: x.name):
            equipment_str = ", ".join([eq.name for eq in exercise.equipment]) if exercise.equipment else "Bodyweight"
            output.append(f"- {exercise.name} (Equipment: {equipment_str})")
    
    return "\n".join(output)
