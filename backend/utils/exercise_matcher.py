"""
Exercise name matching utility to normalize exercise names.

This module provides functions to match exercise names with existing exercises
in the database using fuzzy string matching to prevent duplicate exercises
with slightly different names.
"""
from typing import List, Optional, Tuple
import re
from difflib import SequenceMatcher
from sqlalchemy.orm import Session
import models
import logging

# Configure logging
logger = logging.getLogger(__name__)

def get_base_name(name: str) -> str:
    """
    Extract the base name of an exercise by removing parenthetical qualifiers.

    Args:
        name: The exercise name with possible qualifiers

    Returns:
        Base exercise name without parenthetical qualifiers
    """
    # Remove text in parentheses
    base_name = re.sub(r'\s*\([^)]*\)', '', name)

    # Remove text after hyphen or dash (often used for variations)
    base_name = re.sub(r'\s*-.*$', '', base_name)

    # Remove text after "with" (e.g., "Bench Press with Dumbbells")
    base_name = re.sub(r'\s+with\s+.*$', '', base_name)

    # Clean up any extra whitespace
    base_name = re.sub(r'\s+', ' ', base_name).strip()

    return base_name

def normalize_exercise_name(name: str) -> str:
    """
    Normalize an exercise name by removing common variations and standardizing format.

    Args:
        name: The exercise name to normalize

    Returns:
        Normalized exercise name
    """
    # Convert to lowercase
    normalized = name.lower()

    # First strip qualifiers like "(Wide Grip)" or "(Close Grip)"
    normalized = get_base_name(normalized)

    # Remove common prefixes/suffixes and standardize terms
    replacements = {
        "dumbbell": "db",
        "barbell": "bb",
        "machine": "",
        "cable": "",
        "exercise": "",
        "weighted": "",
        "bodyweight": "bw",
        "body weight": "bw",
        "body-weight": "bw",
        "ez bar": "ez",
        "ez-bar": "ez",
    }

    for old, new in replacements.items():
        normalized = re.sub(r'\b' + old + r'\b', new, normalized)

    # Remove special characters and extra spaces
    normalized = re.sub(r'[^\w\s]', '', normalized)
    normalized = re.sub(r'\s+', ' ', normalized).strip()

    # Fix specific cases like "pull-up" -> "pull up"
    normalized = normalized.replace("pullup", "pull up")

    # Fix specific cases for lat pulldown
    if "lat pull" in normalized or "latpull" in normalized:
        normalized = normalized.replace("lat pull", "lat pulldown")
        normalized = normalized.replace("latpull", "lat pulldown")

    # Fix double "down" in lat pulldown
    normalized = normalized.replace("pulldowndown", "pulldown")

    # Preserve cable crossover variations
    if "cable crossover" in normalized:
        if "high to low" in normalized:
            normalized = "cable crossover high to low"
        elif "low to high" in normalized:
            normalized = "cable crossover low to high"

    # Fix bodyweight abbreviation
    if "body-weight" in normalized:
        normalized = normalized.replace("body-weight", "bw")
    elif normalized.startswith("body "):
        normalized = "bw " + normalized[5:]
    elif "body weight" in normalized or "bodyweight" in normalized:
        normalized = normalized.replace("body weight", "bw")
        normalized = normalized.replace("bodyweight", "bw")

    # Fix pull-up normalization
    if normalized == "pull":
        normalized = "pull up"

    # Fix grip variations in the name
    if "wide grip" in normalized:
        normalized = normalized.replace("wide grip ", "")
    if "close grip" in normalized:
        normalized = normalized.replace("close grip ", "")

    return normalized

def find_matching_exercise(
    exercise_name: str,
    db: Session,
    threshold: float = 0.8,
    debug: bool = False
) -> Optional[models.ExerciseLibrary]:
    """
    Find the closest matching exercise in the database using fuzzy string matching.

    Args:
        exercise_name: The exercise name to match
        db: Database session
        threshold: Similarity threshold (0-1) for matching
        debug: Whether to log detailed matching information

    Returns:
        Matching exercise from the database or None if no match found
    """
    if debug:
        logger.info(f"Finding match for: '{exercise_name}'")

    # First try to find a match with the base name (without qualifiers)
    base_name = get_base_name(exercise_name)
    if base_name != exercise_name and debug:
        logger.info(f"Extracted base name: '{exercise_name}' -> '{base_name}'")

    # Normalize the input exercise name
    normalized_input = normalize_exercise_name(exercise_name)
    if debug and normalized_input != exercise_name:
        logger.info(f"Normalized: '{exercise_name}' -> '{normalized_input}'")

    # Get all exercises from the library
    exercises = db.query(models.ExerciseLibrary).all()

    # Check for specific variations
    is_wide_grip = "wide grip" in exercise_name.lower() or "(wide)" in exercise_name.lower()
    is_close_grip = "close grip" in exercise_name.lower() or "(close)" in exercise_name.lower()
    is_high_to_low = "high to low" in exercise_name.lower() or "(high to low)" in exercise_name.lower()
    is_low_to_high = "low to high" in exercise_name.lower() or "(low to high)" in exercise_name.lower()

    # First pass: Try to find an exact match with the base name and variation
    for exercise in exercises:
        # Check for exact match with grip variation
        if is_wide_grip and "wide grip" in exercise.name.lower():
            if base_name.lower() in exercise.name.lower():
                if debug:
                    logger.info(f"✅ Found wide grip match: '{exercise.name}'")
                return exercise
        elif is_close_grip and "close grip" in exercise.name.lower():
            if base_name.lower() in exercise.name.lower():
                if debug:
                    logger.info(f"✅ Found close grip match: '{exercise.name}'")
                return exercise
        # Check for cable crossover variations
        elif "cable crossover" in exercise_name.lower() and "cable crossover" in exercise.name.lower():
            # Match high to low variation
            if is_high_to_low and "high to low" in exercise.name.lower():
                if debug:
                    logger.info(f"✅ Found high to low cable crossover match: '{exercise.name}'")
                return exercise
            # Match low to high variation
            elif is_low_to_high and "low to high" in exercise.name.lower():
                if debug:
                    logger.info(f"✅ Found low to high cable crossover match: '{exercise.name}'")
                return exercise
        # Check for exact base name match
        elif exercise.name.lower() == base_name.lower():
            if debug:
                logger.info(f"✅ Found exact base name match: '{exercise.name}'")
            return exercise

    # Second pass: Try to find a match with normalized names
    best_match = None
    best_score = 0
    matches = []

    for exercise in exercises:
        # Normalize the database exercise name
        normalized_db = normalize_exercise_name(exercise.name)

        # Check for exact match after normalization (highest priority)
        if normalized_input == normalized_db:
            if debug:
                logger.info(f"✅ Found exact normalized match: '{exercise.name}'")
            return exercise

        # Calculate similarity score
        similarity = SequenceMatcher(None, normalized_input, normalized_db).ratio()

        # Store all potential matches for debugging
        if debug and similarity >= threshold:
            matches.append((exercise.name, similarity))

        # Update best match if this is better
        if similarity > best_score and similarity >= threshold:
            best_score = similarity
            best_match = exercise

    # Log matching results if in debug mode
    if debug:
        if matches:
            logger.info(f"Potential matches for '{exercise_name}':")
            for match_name, score in sorted(matches, key=lambda x: x[1], reverse=True):
                logger.info(f"  - '{match_name}' (score: {score:.2f})")

        if best_match:
            logger.info(f"✅ Best match: '{best_match.name}' (score: {best_score:.2f})")
        else:
            logger.info(f"❌ No match found for '{exercise_name}'")

    return best_match

def get_or_create_exercise(
    exercise_name: str,
    exercise_type: str,
    db: Session,
    match_threshold: float = 0.8
) -> Tuple[models.ExerciseLibrary, bool]:
    """
    Get an existing exercise from the database or create a new one if no match is found.

    Args:
        exercise_name: The exercise name to match
        exercise_type: The type of exercise (strength, cardio, etc.)
        db: Database session
        match_threshold: Similarity threshold for matching

    Returns:
        Tuple of (exercise, is_new) where is_new is True if a new exercise was created
    """
    # Try to find a matching exercise
    matching_exercise = find_matching_exercise(exercise_name, db, match_threshold)

    if matching_exercise:
        return matching_exercise, False

    # No match found, create a new exercise
    new_exercise = models.ExerciseLibrary(
        name=exercise_name,
        exercise_type=exercise_type
    )

    db.add(new_exercise)
    db.commit()
    db.refresh(new_exercise)

    return new_exercise, True
