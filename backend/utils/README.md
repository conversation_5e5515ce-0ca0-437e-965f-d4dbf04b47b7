# Utility Modules

## Exercise Matcher

The `exercise_matcher.py` module provides functionality to normalize exercise names and match similar exercises to prevent duplicate entries in the database.

### Problem Solved

When AI generates workout plans, it might suggest exercises with slightly different names than what's already in the user's database (e.g., "Bicep Curl" vs "Dumbbell Curl"). Without normalization, these would be treated as different exercises, causing incorrect personal record alerts since the system would think they're new exercises.

### How It Works

1. **Normalization**: The `normalize_exercise_name()` function standardizes exercise names by:
   - Converting to lowercase
   - Removing common prefixes/suffixes (e.g., "dumbbell", "barbell", "machine")
   - Standardizing terms (e.g., "bodyweight" → "bw")
   - Removing special characters

2. **Fuzzy Matching**: The `find_matching_exercise()` function:
   - Normalizes both the input exercise name and database exercise names
   - Uses string similarity algorithms to find the closest match
   - Returns the matching exercise if similarity is above a threshold

3. **Integration**: The matcher is used in:
   - AI-generated workout creation
   - Manual exercise addition
   - Personal record tracking

### Usage

```python
from utils.exercise_matcher import find_matching_exercise, normalize_exercise_name

# Normalize an exercise name
normalized = normalize_exercise_name("Dumbbell Bicep Curl")  # Returns "db bicep curl"

# Find a matching exercise in the database
matching_exercise = find_matching_exercise("Dumbbell Bicep Curl", db_session)
if matching_exercise:
    # Use the matched exercise name
    exercise_name = matching_exercise.name
else:
    # No match found, use the original name
    exercise_name = "Dumbbell Bicep Curl"
```

### Testing

You can test the exercise matcher using the `test_exercise_matcher.py` script:

```bash
python backend/test_exercise_matcher.py
```
