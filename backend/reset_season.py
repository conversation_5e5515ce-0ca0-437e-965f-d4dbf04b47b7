#!/usr/bin/env python3
# backend/reset_season.py
"""
Season Reset Script for Workout Tracker

This script resets all points, achievements, and scoring data while preserving
user accounts, workout history, and other core data.

Usage:
    python reset_season.py [--confirm]
    
Options:
    --confirm    Skip confirmation prompt (for automated use)
"""

import sys
import os
import logging
import argparse
from datetime import datetime
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import models and database
from database import SessionLocal, engine
import models

class SeasonReset:
    """Class to handle season reset operations"""
    
    def __init__(self):
        self.db = SessionLocal()
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.db.close()
    
    def get_current_stats(self) -> Dict[str, int]:
        """Get current database statistics"""
        try:
            stats = {
                'users': self.db.query(models.User).count(),
                'workouts': self.db.query(models.Workout).count(),
                'workout_scores': self.db.query(models.WorkoutScore).count(),
                'user_achievements': self.db.query(models.UserAchievement).count(),
                'milestones': self.db.query(models.Milestone).count(),
                'user_stats': self.db.query(models.UserStat).count(),
            }
            return stats
        except Exception as e:
            logger.error(f"Error getting current stats: {str(e)}")
            return {}
    
    def display_current_stats(self):
        """Display current database statistics"""
        logger.info("Current database statistics:")
        stats = self.get_current_stats()
        
        for key, value in stats.items():
            logger.info(f"  {key.replace('_', ' ').title()}: {value}")
    
    def reset_season_data(self) -> bool:
        """
        Reset all season-related data
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info("Starting season reset...")
            
            # Begin transaction
            self.db.begin()
            
            # Delete workout scores
            workout_scores_deleted = self.db.query(models.WorkoutScore).delete()
            logger.info(f"Deleted {workout_scores_deleted} workout scores")
            
            # Delete user achievements (earned achievements)
            user_achievements_deleted = self.db.query(models.UserAchievement).delete()
            logger.info(f"Deleted {user_achievements_deleted} user achievements")
            
            # Delete milestones
            milestones_deleted = self.db.query(models.Milestone).delete()
            logger.info(f"Deleted {milestones_deleted} milestones")
            
            # Reset user stats
            user_stats = self.db.query(models.UserStat).all()
            stats_updated = 0
            
            for stat in user_stats:
                stat.total_score = 0
                stat.current_streak = 0
                stat.longest_streak = 0
                stat.total_workouts = 0
                stat.unique_exercises = 0
                stat.last_updated = datetime.utcnow()
                stats_updated += 1
            
            logger.info(f"Reset {stats_updated} user stat records")
            
            # Commit transaction
            self.db.commit()
            
            logger.info("Season reset completed successfully!")
            return True
            
        except Exception as e:
            # Rollback on error
            self.db.rollback()
            logger.error(f"Error during season reset: {str(e)}")
            return False
    
    def verify_reset(self) -> bool:
        """
        Verify that the season reset was successful
        
        Returns:
            bool: True if verification passed, False otherwise
        """
        try:
            logger.info("Verifying season reset...")
            
            # Check that scoring tables are empty
            workout_scores_count = self.db.query(models.WorkoutScore).count()
            user_achievements_count = self.db.query(models.UserAchievement).count()
            milestones_count = self.db.query(models.Milestone).count()
            
            # Check that user stats are reset
            non_zero_stats = self.db.query(models.UserStat).filter(
                (models.UserStat.total_score > 0) |
                (models.UserStat.current_streak > 0) |
                (models.UserStat.longest_streak > 0)
            ).count()
            
            if (workout_scores_count == 0 and 
                user_achievements_count == 0 and 
                milestones_count == 0 and 
                non_zero_stats == 0):
                
                logger.info("Season reset verification passed!")
                logger.info("All scoring data has been successfully reset")
                return True
            else:
                logger.error("Season reset verification failed!")
                logger.error(f"  Workout Scores remaining: {workout_scores_count}")
                logger.error(f"  User Achievements remaining: {user_achievements_count}")
                logger.error(f"  Milestones remaining: {milestones_count}")
                logger.error(f"  Non-zero user stats: {non_zero_stats}")
                return False
                
        except Exception as e:
            logger.error(f"Error during verification: {str(e)}")
            return False
    
    def create_backup_info(self) -> str:
        """
        Create backup information file
        
        Returns:
            str: Backup information
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_info = f"""
Season Reset Backup Information
Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
Timestamp: {timestamp}

To create a manual backup before running this script, use:
pg_dump -h db -U postgres -d workout_db -t workout_scores --data-only > workout_scores_backup_{timestamp}.sql
pg_dump -h db -U postgres -d workout_db -t user_achievements --data-only > user_achievements_backup_{timestamp}.sql
pg_dump -h db -U postgres -d workout_db -t milestones --data-only > milestones_backup_{timestamp}.sql
pg_dump -h db -U postgres -d workout_db -t user_stats --data-only > user_stats_backup_{timestamp}.sql

To restore from backup:
psql -h db -U postgres -d workout_db -f workout_scores_backup_{timestamp}.sql
psql -h db -U postgres -d workout_db -f user_achievements_backup_{timestamp}.sql
psql -h db -U postgres -d workout_db -f milestones_backup_{timestamp}.sql
psql -h db -U postgres -d workout_db -f user_stats_backup_{timestamp}.sql
"""
        return backup_info

def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Reset season data for workout tracker')
    parser.add_argument('--confirm', action='store_true', 
                       help='Skip confirmation prompt (for automated use)')
    
    args = parser.parse_args()
    
    logger.info("=" * 50)
    logger.info("    Workout Tracker Season Reset")
    logger.info("=" * 50)
    
    try:
        with SeasonReset() as reset_service:
            # Display current stats
            reset_service.display_current_stats()
            
            # Show backup information
            backup_info = reset_service.create_backup_info()
            logger.info("\nBackup Information:")
            logger.info(backup_info)
            
            # Confirmation prompt
            if not args.confirm:
                logger.warning("\nThis will reset ALL points, achievements, and scoring data!")
                logger.warning("User accounts, workouts, and exercise data will be preserved.")
                
                confirm = input("\nAre you sure you want to proceed? (yes/no): ")
                if confirm.lower() != 'yes':
                    logger.info("Season reset cancelled by user")
                    sys.exit(0)
            
            # Perform reset
            if reset_service.reset_season_data():
                # Verify reset
                if reset_service.verify_reset():
                    logger.info("\n" + "=" * 50)
                    logger.info("    Season Reset Completed Successfully!")
                    logger.info("=" * 50)
                    logger.info("\nNext steps:")
                    logger.info("1. Run: python seed_scoring.py")
                    logger.info("2. Run: python calculate_existing_scores.py")
                else:
                    logger.error("Season reset verification failed!")
                    sys.exit(1)
            else:
                logger.error("Season reset failed!")
                sys.exit(1)
                
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
