# backend/calculate_existing_scores.py
import sys
import os
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import models and database
from database import SessionLocal, engine
import models
from scoring_service import ScoringService

def calculate_scores_for_existing_workouts():
    """Calculate scores for all existing completed workouts"""
    db = SessionLocal()
    try:
        # Get all completed workouts that don't have scores
        workouts = db.query(models.Workout).filter(
            models.Workout.status == models.WorkoutStatus.completed
        ).outerjoin(
            models.WorkoutScore,
            models.Workout.id == models.WorkoutScore.workout_id
        ).filter(
            models.WorkoutScore.id == None
        ).all()

        if not workouts:
            logger.info("No workouts found that need scoring")
            return

        logger.info(f"Found {len(workouts)} workouts that need scoring")

        # Create scoring service
        scoring_service = ScoringService(db)

        # Calculate scores for each workout
        success_count = 0
        error_count = 0

        for workout in workouts:
            try:
                logger.info(f"Calculating score for workout {workout.id}")
                score_result = scoring_service.calculate_workout_score(workout.id)
                if score_result:
                    logger.info(f"Workout {workout.id} scored: {score_result['total_score']} points")
                    success_count += 1
                else:
                    logger.warning(f"No score result for workout {workout.id}")
                    error_count += 1
            except Exception as e:
                logger.error(f"Error calculating score for workout {workout.id}: {str(e)}")
                error_count += 1

        logger.info(f"Scoring completed: {success_count} successful, {error_count} errors")
    except Exception as e:
        logger.error(f"Error calculating scores: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    # Run the seed script first to ensure achievements and milestones exist
    from seed_scoring import seed_achievements, initialize_user_stats, create_default_milestones_for_all_users

    # Seed achievements
    seed_achievements()

    # Initialize user stats
    initialize_user_stats()

    # Create default milestones for all users
    create_default_milestones_for_all_users()

    # Calculate scores for existing workouts
    calculate_scores_for_existing_workouts()

    logger.info("Score calculation completed")
