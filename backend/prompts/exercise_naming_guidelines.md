# Exercise Naming Guidelines

When generating or referring to exercises, please follow these naming conventions based on the existing exercise database:

## Basic Format
- Use proper capitalization for exercise names (e.g., "Bench Press" not "bench press")
- Use standard exercise names that match common fitness terminology

## Equipment Prefixes
- When specifying equipment, place it before the exercise name
- Examples:
  - "Barbell Bench Press"
  - "Dumbbell Shoulder Press"
  - "Cable Lat Pulldown"
  - "EZ Curl Bar Preacher Curl"

## Grip and Direction Variations
- For grip variations, use the format: "Equipment Exercise (Grip Type)"
- Examples:
  - "Cable Lat Pulldown (Close Grip)"
  - "Cable Lat Pulldown (Wide Grip)"
  - "EZ Curl Bar Curl (Wide Grip)"
  - "Cable Crossover (High to Low)"
  - "Cable Crossover (Low to High)"

## Exercise Variations
- For many exercise variations, use the specific name rather than parenthetical notation
- Examples:
  - "Front Squat" (not "Squat (Front)")
  - "Incline Barbell Bench Press" (not "Barbell Bench Press (Incline)")
  - "Decline Bench Press" (not "Bench Press (Decline)")
  - "Close Grip Bench Press" (not "Bench Press (Close Grip)")

## Specific Naming Patterns
- Some exercises have specific naming patterns:
  - "Dumbbell Row (One Arm)" uses parentheses
  - "Dumbbell Curl (Alternating)" uses parentheses
  - "Cable Crunch (Kneeling)" uses parentheses
  - "Push-Up Variations" is a general category

## Curl Exercises
- For curl exercises, use "Curl" not "Bicep Curl"
- Examples:
  - "Barbell Curl" (not "Barbell Bicep Curl")
  - "Dumbbell Curl" (not "Dumbbell Bicep Curl")
  - "Cable Curl (Straight Bar)" (not "Cable Bicep Curl")
  - "EZ Curl Bar Curl" (not "EZ Curl Bar Bicep Curl")
  - "Hammer Curl" (for hammer grip curls)

## Consistency
- Always use the same name for the same exercise
- Avoid using abbreviations or shorthand
- Be specific about the exercise variation when relevant

Following these guidelines ensures that exercises are properly matched in the database, which improves tracking, analytics, and personal record detection.
