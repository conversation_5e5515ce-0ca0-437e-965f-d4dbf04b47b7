"""Add exercise matcher and expand exercise library

Revision ID: 41fb6e6ffbf0
Revises: dac0c7d3a4ac
Create Date: 2025-05-03 21:37:01.742716

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '41fb6e6ffbf0'
down_revision: Union[str, None] = 'dac0c7d3a4ac'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Create muscle_groups table if it doesn't exist
    op.create_table('muscle_groups',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_muscle_groups_id'), 'muscle_groups', ['id'], unique=False)

    # Create equipment table if it doesn't exist
    op.create_table('equipment',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_equipment_id'), 'equipment', ['id'], unique=False)

    # Create exercise_library table if it doesn't exist
    op.create_table('exercise_library',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('instructions', sa.Text(), nullable=True),
        sa.Column('exercise_type', sa.String(), nullable=False),
        sa.Column('difficulty', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_exercise_library_id'), 'exercise_library', ['id'], unique=False)

    # Create junction table for exercise-muscle groups
    op.create_table('exercise_muscle_groups',
        sa.Column('exercise_id', sa.Integer(), nullable=False),
        sa.Column('muscle_group_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['exercise_id'], ['exercise_library.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['muscle_group_id'], ['muscle_groups.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('exercise_id', 'muscle_group_id')
    )

    # Create junction table for exercise-equipment
    op.create_table('exercise_equipment',
        sa.Column('exercise_id', sa.Integer(), nullable=False),
        sa.Column('equipment_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['exercise_id'], ['exercise_library.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['equipment_id'], ['equipment.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('exercise_id', 'equipment_id')
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('exercise_equipment')
    op.drop_table('exercise_muscle_groups')
    op.drop_index(op.f('ix_exercise_library_id'), table_name='exercise_library')
    op.drop_table('exercise_library')
    op.drop_index(op.f('ix_equipment_id'), table_name='equipment')
    op.drop_table('equipment')
    op.drop_index(op.f('ix_muscle_groups_id'), table_name='muscle_groups')
    op.drop_table('muscle_groups')
    # ### end Alembic commands ###
