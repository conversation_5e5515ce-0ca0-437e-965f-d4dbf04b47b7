"""Add exercise matcher improvements

Revision ID: 42188285ed14
Revises: 41fb6e6ffbf0
Create Date: 2025-05-03 21:53:23.300961

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '42188285ed14'
down_revision: Union[str, None] = '41fb6e6ffbf0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
