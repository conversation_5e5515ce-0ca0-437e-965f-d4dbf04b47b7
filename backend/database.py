# backend/database.py
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
import time

DATABASE_URL = os.getenv("DATABASE_URL", "********************************************")

# Add retry mechanism
max_retries = 5
retry_count = 0
retry_delay = 5  # seconds

while retry_count < max_retries:
    try:
        print(f"Attempting to connect to database (attempt {retry_count + 1}/{max_retries})...")
        engine = create_engine(DATABASE_URL)
        # Test the connection
        with engine.connect() as conn:
            print("Database connection successful!")
        break
    except Exception as e:
        retry_count += 1
        if retry_count < max_retries:
            print(f"Failed to connect to database: {e}")
            print(f"Retrying in {retry_delay} seconds...")
            time.sleep(retry_delay)
        else:
            print(f"Failed to connect to database after {max_retries} attempts: {e}")
            raise

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()