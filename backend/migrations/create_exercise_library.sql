-- Create muscle_groups table
CREATE TABLE IF NOT EXISTS muscle_groups (
    id SERIAL PRIMARY KEY,
    name VARCHAR NOT NULL UNIQUE,
    description TEXT
);

-- Create equipment table
CREATE TABLE IF NOT EXISTS equipment (
    id SERIAL PRIMARY KEY,
    name VARCHAR NOT NULL UNIQUE,
    description TEXT
);

-- Create exercise_library table
CREATE TABLE IF NOT EXISTS exercise_library (
    id SERIAL PRIMARY KEY,
    name VARCHAR NOT NULL,
    description TEXT,
    instructions TEXT,
    exercise_type VARCHAR NOT NULL,
    difficulty VARCHAR
);

-- Create junction table for exercise-muscle groups
CREATE TABLE IF NOT EXISTS exercise_muscle_groups (
    exercise_id INTEGER REFERENCES exercise_library(id) ON DELETE CASCADE,
    muscle_group_id INTEGER REFERENCES muscle_groups(id) ON DELETE CASCADE,
    PRIMARY KEY (exercise_id, muscle_group_id)
);

-- Create junction table for exercise-equipment
CREATE TABLE IF NOT EXISTS exercise_equipment (
    exercise_id INTEGER REFERENCES exercise_library(id) ON DELETE CASCADE,
    equipment_id INTEGER REFERENCES equipment(id) ON DELETE CASCADE,
    PRIMARY KEY (exercise_id, equipment_id)
);

-- Insert common muscle groups
INSERT INTO muscle_groups (name, description) VALUES
('chest', 'Pectoralis major and minor muscles'),
('back', 'Latissimus dorsi, rhomboids, and trapezius muscles'),
('shoulders', 'Deltoid muscles (anterior, lateral, and posterior)'),
('biceps', 'Biceps brachii muscles'),
('triceps', 'Triceps brachii muscles'),
('forearms', 'Muscles of the forearm'),
('abs', 'Abdominal muscles including rectus abdominis and obliques'),
('legs', 'Quadriceps, hamstrings, and calves'),
('glutes', 'Gluteal muscles'),
('calves', 'Gastrocnemius and soleus muscles'),
('full_body', 'Exercises that work multiple major muscle groups')
ON CONFLICT (name) DO NOTHING;

-- Insert common equipment
INSERT INTO equipment (name, description) VALUES
('none', 'No equipment needed (bodyweight exercises)'),
('dumbbells', 'Free weights that can be held in each hand'),
('barbell', 'Long bar with weights on each end'),
('kettlebell', 'Cast iron or steel ball with a handle'),
('resistance_bands', 'Elastic bands that provide resistance'),
('machines', 'Weight machines found in gyms'),
('bench', 'Flat or adjustable workout bench'),
('pull_up_bar', 'Bar for performing pull-ups and chin-ups'),
('stability_ball', 'Large inflatable ball for core exercises'),
('medicine_ball', 'Weighted ball for dynamic exercises'),
('cable_machine', 'Machine with adjustable cables and attachments'),
('smith_machine', 'Barbell fixed within steel rails'),
('treadmill', 'Machine for walking or running'),
('stationary_bike', 'Stationary exercise bicycle'),
('elliptical', 'Low-impact cardio machine'),
('rowing_machine', 'Machine that simulates rowing')
ON CONFLICT (name) DO NOTHING;

-- Insert sample exercises (strength)
INSERT INTO exercise_library (name, description, instructions, exercise_type, difficulty) VALUES
('Bench Press', 'A compound exercise that targets the chest, shoulders, and triceps', 
'1. Lie on a flat bench with feet flat on the floor
2. Grip the barbell slightly wider than shoulder-width
3. Lower the bar to your mid-chest
4. Press the bar back up to starting position', 
'strength', 'intermediate'),

('Squat', 'A compound lower body exercise that primarily targets the quadriceps, hamstrings, and glutes', 
'1. Stand with feet shoulder-width apart
2. Lower your body by bending your knees and hips
3. Keep your back straight and knees in line with toes
4. Return to standing position', 
'strength', 'intermediate'),

('Deadlift', 'A compound exercise that works the entire posterior chain', 
'1. Stand with feet hip-width apart, barbell over mid-foot
2. Bend at hips and knees to grip the bar
3. Keep back flat and lift by extending hips and knees
4. Return the weight to the floor with control', 
'strength', 'advanced'),

('Push-up', 'A bodyweight exercise that targets the chest, shoulders, and triceps', 
'1. Start in a plank position with hands slightly wider than shoulders
2. Lower your body until chest nearly touches the floor
3. Push back up to starting position
4. Keep body in a straight line throughout', 
'strength', 'beginner')
ON CONFLICT DO NOTHING;

-- Insert sample exercises (cardio)
INSERT INTO exercise_library (name, description, instructions, exercise_type, difficulty) VALUES
('Running', 'A cardiovascular exercise that improves endurance and burns calories', 
'1. Maintain good posture with slight forward lean
2. Land midfoot with each step
3. Keep arms at 90-degree angles
4. Breathe rhythmically', 
'cardio', 'beginner'),

('Cycling', 'A low-impact cardiovascular exercise that targets the lower body', 
'1. Adjust seat height so legs are almost fully extended at bottom of pedal stroke
2. Keep back straight and core engaged
3. Pedal in smooth, circular motions
4. Maintain steady breathing', 
'cardio', 'beginner')
ON CONFLICT DO NOTHING;

-- Connect exercises to muscle groups
INSERT INTO exercise_muscle_groups (exercise_id, muscle_group_id)
SELECT e.id, m.id FROM exercise_library e, muscle_groups m
WHERE e.name = 'Bench Press' AND m.name = 'chest'
ON CONFLICT DO NOTHING;

INSERT INTO exercise_muscle_groups (exercise_id, muscle_group_id)
SELECT e.id, m.id FROM exercise_library e, muscle_groups m
WHERE e.name = 'Squat' AND m.name = 'legs'
ON CONFLICT DO NOTHING;

INSERT INTO exercise_muscle_groups (exercise_id, muscle_group_id)
SELECT e.id, m.id FROM exercise_library e, muscle_groups m
WHERE e.name = 'Deadlift' AND m.name = 'back'
ON CONFLICT DO NOTHING;

INSERT INTO exercise_muscle_groups (exercise_id, muscle_group_id)
SELECT e.id, m.id FROM exercise_library e, muscle_groups m
WHERE e.name = 'Push-up' AND m.name = 'chest'
ON CONFLICT DO NOTHING;

-- Connect exercises to equipment
INSERT INTO exercise_equipment (exercise_id, equipment_id)
SELECT e.id, eq.id FROM exercise_library e, equipment eq
WHERE e.name = 'Bench Press' AND eq.name = 'barbell'
ON CONFLICT DO NOTHING;

INSERT INTO exercise_equipment (exercise_id, equipment_id)
SELECT e.id, eq.id FROM exercise_library e, equipment eq
WHERE e.name = 'Squat' AND eq.name = 'barbell'
ON CONFLICT DO NOTHING;

INSERT INTO exercise_equipment (exercise_id, equipment_id)
SELECT e.id, eq.id FROM exercise_library e, equipment eq
WHERE e.name = 'Deadlift' AND eq.name = 'barbell'
ON CONFLICT DO NOTHING;

INSERT INTO exercise_equipment (exercise_id, equipment_id)
SELECT e.id, eq.id FROM exercise_library e, equipment eq
WHERE e.name = 'Push-up' AND eq.name = 'none'
ON CONFLICT DO NOTHING;
