from sqlalchemy import Column, Integer, String, Text, ForeignKey, Table
from sqlalchemy.orm import relationship
from database import Base

# Many-to-many relationship table for exercise-muscle groups
exercise_muscle_groups = Table(
    'exercise_muscle_groups',
    Base.metadata,
    Column('exercise_id', Integer, ForeignKey('exercise_library.id', ondelete="CASCADE"), primary_key=True),
    Column('muscle_group_id', Integer, ForeignKey('muscle_groups.id', ondelete="CASCADE"), primary_key=True)
)

# Many-to-many relationship table for exercise-equipment
exercise_equipment = Table(
    'exercise_equipment',
    Base.metadata,
    Column('exercise_id', Integer, ForeignKey('exercise_library.id', ondelete="CASCADE"), primary_key=True),
    Column('equipment_id', Integer, ForeignKey('equipment.id', ondelete="CASCADE"), primary_key=True)
)

class MuscleGroup(Base):
    __tablename__ = "muscle_groups"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, nullable=False)
    description = Column(Text, nullable=True)
    
    # Relationship to exercises
    exercises = relationship(
        "ExerciseLibrary", 
        secondary=exercise_muscle_groups,
        back_populates="muscle_groups"
    )

class Equipment(Base):
    __tablename__ = "equipment"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, nullable=False)
    description = Column(Text, nullable=True)
    
    # Relationship to exercises
    exercises = relationship(
        "ExerciseLibrary", 
        secondary=exercise_equipment,
        back_populates="equipment"
    )

class ExerciseLibrary(Base):
    __tablename__ = "exercise_library"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    instructions = Column(Text, nullable=True)
    exercise_type = Column(String, nullable=False)  # strength, cardio, flexibility, other
    difficulty = Column(String, nullable=True)  # beginner, intermediate, advanced
    
    # Relationships
    muscle_groups = relationship(
        "MuscleGroup", 
        secondary=exercise_muscle_groups,
        back_populates="exercises"
    )
    
    equipment = relationship(
        "Equipment", 
        secondary=exercise_equipment,
        back_populates="exercises"
    )
