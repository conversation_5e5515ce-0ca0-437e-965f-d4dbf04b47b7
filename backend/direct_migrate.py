# backend/direct_migrate.py
import psycopg2
import os
import time

# This script can be used to apply the migration directly if you don't have Al<PERSON>bic set up

def apply_migration():
    # Get database connection details from environment variables
    db_host = os.getenv("POSTGRES_HOST", "db")
    db_port = os.getenv("POSTGRES_PORT", "5432")
    db_name = os.getenv("POSTGRES_DB", "workout_db")
    db_user = os.getenv("POSTGRES_USER", "postgres")
    db_password = os.getenv("POSTGRES_PASSWORD", "postgres")
    
    # Connect to the database
    max_retries = 5
    retry_count = 0
    retry_delay = 5  # seconds
    
    while retry_count < max_retries:
        try:
            print(f"Attempting to connect to database (attempt {retry_count + 1}/{max_retries})...")
            conn = psycopg2.connect(
                host=db_host,
                port=db_port,
                dbname=db_name,
                user=db_user,
                password=db_password
            )
            conn.autocommit = True
            cursor = conn.cursor()
            print("Database connection successful!")
            break
        except Exception as e:
            retry_count += 1
            if retry_count < max_retries:
                print(f"Failed to connect to database: {e}")
                print(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
            else:
                print(f"Failed to connect to database after {max_retries} attempts: {e}")
                raise
    
    try:
        # Check if status column already exists
        cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name='workouts' AND column_name='status';")
        status_exists = cursor.fetchone() is not None
        
        # Add status column to workouts table if it doesn't exist
        if not status_exists:
            print("Adding 'status' column to workouts table...")
            cursor.execute("ALTER TABLE workouts ADD COLUMN status VARCHAR DEFAULT 'completed';")
        else:
            print("Status column already exists in workouts table.")
        
        # Check if exercises table exists
        cursor.execute("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'exercises');")
        exercises_exists = cursor.fetchone()[0]
        
        # Create exercises table if it doesn't exist
        if not exercises_exists:
            print("Creating exercises table...")
            cursor.execute("""
                CREATE TABLE exercises (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR NOT NULL,
                    exercise_type VARCHAR NOT NULL DEFAULT 'strength',
                    workout_id INTEGER REFERENCES workouts(id) ON DELETE CASCADE
                );
            """)
        else:
            print("Exercises table already exists.")
        
        # Check if exercise_sets table exists
        cursor.execute("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'exercise_sets');")
        sets_exists = cursor.fetchone()[0]
        
        # Create exercise_sets table if it doesn't exist
        if not sets_exists:
            print("Creating exercise_sets table...")
            cursor.execute("""
                CREATE TABLE exercise_sets (
                    id SERIAL PRIMARY KEY,
                    reps INTEGER,
                    weight FLOAT,
                    duration_seconds INTEGER,
                    distance FLOAT,
                    notes TEXT,
                    exercise_id INTEGER REFERENCES exercises(id) ON DELETE CASCADE
                );
            """)
        else:
            print("Exercise_sets table already exists.")
        
        print("Migration completed successfully!")
        
    except Exception as e:
        print(f"Error during migration: {e}")
        raise
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    apply_migration()
