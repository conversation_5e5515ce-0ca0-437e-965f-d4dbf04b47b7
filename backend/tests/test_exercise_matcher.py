"""
Test script for the exercise matcher utility.

This script tests the exercise name normalization and matching functionality.
"""
import sys
import os

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from database import get_db
from utils.exercise_matcher import normalize_exercise_name, find_matching_exercise, get_base_name

def test_get_base_name():
    """Test the get_base_name function with various inputs."""
    test_cases = [
        ("Lat Pulldown", "Lat Pulldown"),
        ("Lat Pulldown (Wide Grip)", "Lat Pulldown"),
        ("Cable Lat Pulldown (Close Grip)", "Cable Lat Pulldown"),
        ("Bench Press with Dumbbells", "Bench Press"),
        ("Tricep Extension - Rope", "Tricep Extension"),
        ("Barbell Curl (EZ Bar)", "Barbell Curl"),
    ]

    print("Testing base name extraction:")
    for original, expected in test_cases:
        base_name = get_base_name(original)
        result = "✅" if base_name == expected else f"❌ (got '{base_name}', expected '{expected}')"
        print(f"  {original} -> {base_name} {result}")

def test_normalize_exercise_name():
    """Test the normalize_exercise_name function with various inputs."""
    test_cases = [
        ("Bicep Curl", "bicep curl"),
        ("Dumbbell Bicep Curl", "db bicep curl"),
        ("Barbell Bench Press", "bb bench press"),
        ("Machine Leg Press", "leg press"),
        ("Cable Tricep Extension", "tricep extension"),
        ("Weighted Pull-Up", "pull up"),
        ("Body-Weight Squat", "bw squat"),
        ("Bodyweight Squat", "bw squat"),
        ("DUMBBELL SHOULDER PRESS", "db shoulder press"),
        # Test cases with qualifiers
        ("Lat Pulldown (Wide Grip)", "lat pulldown"),
        ("Cable Lat Pulldown (Close Grip)", "lat pulldown"),
    ]

    print("Testing exercise name normalization:")
    for original, expected in test_cases:
        normalized = normalize_exercise_name(original)
        result = "✅" if normalized == expected else f"❌ (got '{normalized}', expected '{expected}')"
        print(f"  {original} -> {normalized} {result}")

def test_find_matching_exercise(db: Session):
    """Test the find_matching_exercise function with the actual database."""
    # Import models here to avoid circular imports
    import models

    # First, get some exercises from the database to use as reference
    exercises = db.query(models.ExerciseLibrary).limit(10).all()
    if not exercises:
        print("No exercises found in the database. Please seed the database first.")
        return

    # Create some variations of the exercises
    test_cases = []
    for ex in exercises:
        name = ex.name
        # Create variations
        variations = [
            f"Dumbbell {name}",
            f"{name} with Dumbbells",
            f"Barbell {name}",
            f"{name} (Weighted)",
            f"{name} (Standard Grip)",
            f"{name} (Wide Grip)",
            f"{name} (Close Grip)",
            f"{name} - Machine",
            name.upper(),
            name.lower(),
        ]
        test_cases.extend([(var, name) for var in variations])

    # Add specific test cases for Lat Pulldown variations
    lat_pulldown_tests = [
        ("Lat Pulldown", "Cable Lat Pulldown (Wide Grip)"),
        ("Cable Lat Pulldown", "Cable Lat Pulldown (Wide Grip)"),
        ("Lat Pull Down", "Cable Lat Pulldown (Wide Grip)"),
        ("Lat Pulldown Machine", "Cable Lat Pulldown (Wide Grip)"),
        ("Wide Grip Lat Pulldown", "Cable Lat Pulldown (Wide Grip)"),
        ("Close Grip Lat Pulldown", "Cable Lat Pulldown (Close Grip)"),
    ]
    test_cases.extend(lat_pulldown_tests)

    print("\nTesting exercise matching with database:")
    for variant, expected in test_cases:
        match = find_matching_exercise(variant, db, debug=True)
        if match:
            result = "✅" if match.name == expected else f"❌ (matched to '{match.name}', expected '{expected}')"
            print(f"  {variant} -> {match.name} {result}")
        else:
            print(f"  {variant} -> No match found ❌")

def main():
    """Run the tests."""
    # Test base name extraction
    test_get_base_name()

    # Test normalization
    test_normalize_exercise_name()

    # Test matching with database
    try:
        print("\nAttempting to connect to database (attempt 1/5)...")
        db = next(get_db())
        print("Database connection successful!")
        test_find_matching_exercise(db)
    except Exception as e:
        print(f"Error connecting to database: {e}")

if __name__ == "__main__":
    main()
