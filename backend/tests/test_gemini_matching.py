#!/usr/bin/env python3
"""
Test script to simulate workout generation with Gemini API and test exercise matching.
This script:
1. Sends a request to Gemini API using the same environment variables as the app
2. Parses the generated workout
3. Tests how well each exercise matches with the database
"""
import os
import sys
import json
from typing import List, Dict, Any, Optional
import logging

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import google.generativeai as genai
from database import get_db
from utils.exercise_matcher import find_matching_exercise
from sqlalchemy.orm import Session

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_gemini_api():
    """Set up the Gemini API client using environment variables."""
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        logger.error("GEMINI_API_KEY environment variable not set")
        sys.exit(1)

    genai.configure(api_key=api_key)

    # Initialize the model - use the same model as in gemini_service.py
    model_name = 'gemini-1.5-pro'  # Hardcoded to match the application
    try:
        logger.info(f"Initializing Gemini model: {model_name}")
        model = genai.GenerativeModel(model_name)
        return model
    except Exception as e:
        logger.error(f"Failed to initialize Gemini model: {e}")
        sys.exit(1)

def get_available_exercises(db: Session, equipment: List[str]):
    """Get exercises that can be performed with the specified equipment."""
    from utils.exercise_list import get_exercises_by_equipment, format_exercises_for_llm

    # Get exercises that can be performed with the specified equipment
    available_exercises = get_exercises_by_equipment(db, equipment)

    # Format the exercises for the LLM
    exercise_list = format_exercises_for_llm(available_exercises)

    return exercise_list, available_exercises

def build_workout_generation_prompt(equipment: List[str], exercise_count: int, goals: str, db: Session):
    """Build the workout generation prompt."""
    # Get available exercises
    exercise_list, _ = get_available_exercises(db, equipment)

    prompt = f"""
    As an expert personal trainer, create a complete workout plan for a user with the following parameters:

    EQUIPMENT AVAILABLE: {', '.join(equipment) if equipment else 'Bodyweight only'}
    EXERCISE COUNT: {exercise_count}
    USER GOALS: {goals}

    AVAILABLE EXERCISES:
{exercise_list}

    Please select exactly {exercise_count} exercises from the AVAILABLE EXERCISES list above.
    Use the exact exercise names as they appear in the list.

    Provide your workout plan using the following format for each exercise:

    EXERCISE: [Name - EXACTLY as it appears in the AVAILABLE EXERCISES list]
    TYPE: [Type - one of: strength, cardio, flexibility, other]
    EQUIPMENT: [Required equipment]
    DESCRIPTION: [Brief description]
    INSTRUCTIONS:
    1. [Step 1]
    2. [Step 2]
    3. [Step 3]
    RECOMMENDATION: [Sets and reps]

    Also include a brief paragraph of general advice for this workout plan.

    IMPORTANT:
    - Do not use markdown formatting like asterisks or pound signs in your response
    - Do not duplicate instruction numbers
    - Provide exactly {exercise_count} exercises, no more and no less
    - Make sure the exercises work well together as a complete workout
    - Consider the user's specific goals: {goals}
    - ONLY use exercise names that appear EXACTLY in the AVAILABLE EXERCISES list
    """

    return prompt

def parse_generated_workout(text: str) -> Dict[str, Any]:
    """Parse the generated workout text into structured data."""
    exercises = []
    general_advice = ""

    # Split the text into sections
    sections = text.split("\n\n")

    for section in sections:
        if "exercise" in section.lower() or ":" in section:
            # This looks like an exercise
            lines = section.strip().split("\n")
            if len(lines) < 3:
                continue

            exercise = {}

            # Extract name from first line
            exercise["name"] = lines[0].split(":", 1)[-1].strip() if ":" in lines[0] else lines[0].strip()

            # Process remaining lines
            instructions = []
            for line in lines[1:]:
                if "type:" in line.lower() or "exercise type:" in line.lower():
                    exercise["type"] = line.split(":", 1)[-1].strip()
                elif "equipment:" in line.lower() or "required equipment:" in line.lower():
                    exercise["equipment"] = line.split(":", 1)[-1].strip()
                elif "description:" in line.lower():
                    exercise["description"] = line.split(":", 1)[-1].strip()
                elif "instruction" in line.lower() and ":" in line:
                    continue  # Skip the header
                elif "set" in line.lower() and "rep" in line.lower():
                    parts = line.split(":", 1)
                    if len(parts) > 1:
                        rec = parts[1].strip()
                        if "set" in rec.lower():
                            try:
                                sets = int(rec.split("sets")[0].strip().split()[-1])
                                exercise["sets"] = sets
                            except:
                                pass

                        if "rep" in rec.lower():
                            try:
                                reps = rec.split("reps")[0].split("sets")[-1].strip()
                                exercise["reps"] = reps
                            except:
                                pass
                elif line.strip() and not any(k in line.lower() for k in ["exercise", "name:"]):
                    # Assume it's an instruction step
                    if line.strip().startswith(("- ", "• ", "* ")):
                        instructions.append(line.strip()[2:])
                    elif line.strip()[0].isdigit() and len(line) > 2 and line[1] in [".", ")"]:
                        instructions.append(line.strip()[2:].strip())
                    else:
                        instructions.append(line.strip())

            if instructions:
                exercise["instructions"] = instructions

            if exercise.get("name"):  # Only add if we have at least a name
                exercises.append(exercise)

        elif "advice" in section.lower() or "tip" in section.lower() or "general" in section.lower():
            # This looks like general advice
            general_advice = section.strip()

    return {
        "exercises": exercises,
        "generalAdvice": general_advice
    }

def test_exercise_matching(exercises: List[Dict[str, Any]], db_session):
    """Test how well each exercise matches with the database."""
    logger.info(f"Testing matching for {len(exercises)} exercises")

    match_results = []

    for exercise in exercises:
        exercise_name = exercise.get("name", "")
        if not exercise_name:
            continue

        # Try to find a matching exercise in the database
        matching_exercise = find_matching_exercise(exercise_name, db_session, debug=True)

        result = {
            "generated_name": exercise_name,
            "matched_name": matching_exercise.name if matching_exercise else None,
            "matched": matching_exercise is not None,
            "exercise_type": exercise.get("type", "unknown")
        }

        match_results.append(result)

        if matching_exercise:
            logger.info(f"✅ Matched '{exercise_name}' to '{matching_exercise.name}'")
        else:
            logger.info(f"❌ No match found for '{exercise_name}'")

    # Calculate match rate
    match_count = sum(1 for r in match_results if r["matched"])
    match_rate = match_count / len(match_results) if match_results else 0

    logger.info(f"Match rate: {match_rate:.2%} ({match_count}/{len(match_results)})")

    return match_results

def main():
    """Main function to run the test."""
    # Parse command line arguments
    import argparse

    parser = argparse.ArgumentParser(description="Test Gemini workout generation and exercise matching")
    parser.add_argument("--equipment", type=str, nargs="+", default=[], help="Available equipment (space-separated)")
    parser.add_argument("--count", type=int, default=5, help="Number of exercises to generate")
    parser.add_argument("--goals", type=str, default="Build strength and muscle", help="Workout goals")
    args = parser.parse_args()

    # Get database session
    db = next(get_db())

    # Set up Gemini API
    model = setup_gemini_api()

    # Build the prompt
    prompt = build_workout_generation_prompt(args.equipment, args.count, args.goals, db)

    # Generate content
    logger.info("Sending request to Gemini API...")
    generation_config = {
        "temperature": 0.3,
        "top_p": 0.95,
        "max_output_tokens": 2048,
    }

    try:
        response = model.generate_content(prompt, generation_config=generation_config)

        if not hasattr(response, 'text'):
            logger.error("No text in response from Gemini API")
            sys.exit(1)

        generated_text = response.text

        # Create output directory if it doesn't exist
        output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "output")
        os.makedirs(output_dir, exist_ok=True)

        # Save the raw response for debugging
        with open(os.path.join(output_dir, "gemini_response.txt"), "w") as f:
            f.write(generated_text)

        logger.info("Successfully generated workout")

        # Parse the generated workout
        workout_data = parse_generated_workout(generated_text)

        # Save the parsed data for debugging
        with open(os.path.join(output_dir, "parsed_workout.json"), "w") as f:
            json.dump(workout_data, f, indent=2)

        logger.info(f"Parsed {len(workout_data['exercises'])} exercises")

        # Test exercise matching
        match_results = test_exercise_matching(workout_data["exercises"], db)

        # Save match results
        with open(os.path.join(output_dir, "match_results.json"), "w") as f:
            json.dump(match_results, f, indent=2)

        logger.info("Test completed successfully")

    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
