# backend/tests/test_cable_crossover.py
import sys
import os

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from database import get_db
from utils.exercise_matcher import find_matching_exercise

def test_cable_crossover_matching():
    """Test the matching of cable crossover exercises."""
    try:
        # Get database session
        db = next(get_db())
        
        # Test cases for cable crossover exercises
        test_cases = [
            "Cable Crossover",
            "Cable Crossover (High to Low)",
            "Cable Crossover (Low to High)",
            "Cable Crossover High to Low",
            "Cable Crossover Low to High",
            "Cable Crossovers",
            "Cable Crossovers (High to Low)",
            "Cable Crossovers (Low to High)",
        ]
        
        print("\nTesting Cable Crossover exercise matching:")
        for exercise_name in test_cases:
            match = find_matching_exercise(exercise_name, db, debug=True)
            if match:
                print(f"  '{exercise_name}' -> '{match.name}' (ID: {match.id})")
            else:
                print(f"  '{exercise_name}' -> No match found")
                
    except Exception as e:
        print(f"Error testing cable crossover matching: {str(e)}")

if __name__ == "__main__":
    test_cable_crossover_matching()
