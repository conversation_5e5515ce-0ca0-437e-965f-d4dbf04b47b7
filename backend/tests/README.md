# Testing Scripts for Workout Tracker

This directory contains testing scripts for the Workout Tracker application.

## Available Tests

### 1. Exercise Matcher Test (`test_exercise_matcher.py`)

Tests the exercise name normalization and matching functionality.

**Usage:**
```bash
docker exec workout_tracker-backend-1 python tests/test_exercise_matcher.py
```

This script tests:
- Base name extraction from exercise names with qualifiers
- Exercise name normalization
- Exercise matching with the database

### 2. Gemini Workout Generation Test (`test_gemini_matching.py`)

Tests the entire workflow from generating a workout with <PERSON> to matching exercises with the database.

**Usage:**
```bash
# Basic usage (generates 5 exercises for strength and muscle building)
docker exec workout_tracker-backend-1 python tests/test_gemini_matching.py

# Specify equipment
docker exec workout_tracker-backend-1 python tests/test_gemini_matching.py --equipment "Barbell" "Dumbbell" "Bench"

# Specify number of exercises
docker exec workout_tracker-backend-1 python tests/test_gemini_matching.py --count 8

# Specify workout goals
docker exec workout_tracker-backend-1 python tests/test_gemini_matching.py --goals "Improve cardio and endurance"

# Combine all options
docker exec workout_tracker-backend-1 python tests/test_gemini_matching.py --equipment "Barbell" "Dumbbell" "Bench" --count 6 --goals "Build upper body strength"
```

This script:
1. Sends a request to the Gemini API using the same prompt and environment variables as the app
2. Saves the raw response to `output/gemini_response.txt`
3. Parses the generated workout and saves it to `output/parsed_workout.json`
4. Tests how well each exercise matches with the database
5. Saves the match results to `output/match_results.json`
6. Displays the match rate (percentage of exercises that matched with the database)

## Output Files

Test output files are stored in the `output` directory:

- `gemini_response.txt`: Raw response from the Gemini API
- `parsed_workout.json`: Parsed workout data
- `match_results.json`: Exercise matching results

## Notes

- These scripts require access to the database and the Gemini API
- The `GEMINI_API_KEY` environment variable must be set
- Run these scripts inside the Docker container to ensure proper access to dependencies
