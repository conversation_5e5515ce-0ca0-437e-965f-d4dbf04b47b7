# backend/tests/test_flexibility_scoring.py
import sys
import os
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import models and database
from database import SessionLocal, engine
import models
from scoring_service import ScoringService

def create_test_workout(db, user_id, exercise_types):
    """
    Create a test workout with the specified exercise types
    
    Args:
        db: Database session
        user_id: User ID to create the workout for
        exercise_types: List of exercise types (e.g., ["strength", "flexibility"])
        
    Returns:
        The created workout
    """
    # Create a new workout
    workout = models.Workout(
        user_id=user_id,
        date=datetime.utcnow(),
        description="Test workout",
        status=models.WorkoutStatus.completed
    )
    db.add(workout)
    db.flush()
    
    # Add exercises
    for i, exercise_type in enumerate(exercise_types):
        exercise = models.Exercise(
            workout_id=workout.id,
            name=f"Test Exercise {i+1}",
            exercise_type=exercise_type
        )
        db.add(exercise)
        
        # Add a set to make it a valid exercise
        exercise_set = models.ExerciseSet(
            exercise_id=exercise.id,
            reps=10,
            weight=50.0
        )
        db.add(exercise_set)
    
    db.commit()
    return workout

def test_flexibility_scoring():
    """Test the scoring reduction for flexibility exercises"""
    db = SessionLocal()
    try:
        # Get or create a test user
        test_user = db.query(models.User).filter(models.User.username == "test_user").first()
        if not test_user:
            test_user = models.User(
                username="test_user",
                hashed_password="test_password",
                is_active=True
            )
            db.add(test_user)
            db.commit()
        
        # Create scoring service
        scoring_service = ScoringService(db)
        
        # Test case 1: All strength exercises
        logger.info("Test case 1: All strength exercises")
        workout1 = create_test_workout(db, test_user.id, ["strength", "strength", "strength"])
        score1 = scoring_service.calculate_workout_score(workout1.id)
        logger.info(f"Score for all strength: {score1['total_score']}")
        logger.info(f"Type modifier: {score1['type_modifier']}")
        
        # Test case 2: Mixed workout (2/3 strength, 1/3 flexibility)
        logger.info("\nTest case 2: Mixed workout (2/3 strength, 1/3 flexibility)")
        workout2 = create_test_workout(db, test_user.id, ["strength", "strength", "flexibility"])
        score2 = scoring_service.calculate_workout_score(workout2.id)
        logger.info(f"Score for mixed workout: {score2['total_score']}")
        logger.info(f"Type modifier: {score2['type_modifier']}")
        
        # Test case 3: All flexibility exercises
        logger.info("\nTest case 3: All flexibility exercises")
        workout3 = create_test_workout(db, test_user.id, ["flexibility", "flexibility", "flexibility"])
        score3 = scoring_service.calculate_workout_score(workout3.id)
        logger.info(f"Score for all flexibility: {score3['total_score']}")
        logger.info(f"Type modifier: {score3['type_modifier']}")
        
        # Verify the results
        logger.info("\nResults summary:")
        logger.info(f"All strength score: {score1['total_score']}")
        logger.info(f"Mixed workout score: {score2['total_score']}")
        logger.info(f"All flexibility score: {score3['total_score']}")
        
        # Check if the flexibility reduction is working
        assert score1['total_score'] > score3['total_score'], "Flexibility workout should score less than strength workout"
        assert score1['total_score'] > score2['total_score'], "Mixed workout should score less than all strength workout"
        assert score2['total_score'] > score3['total_score'], "Mixed workout should score more than all flexibility workout"
        
        logger.info("All tests passed!")
        
    except Exception as e:
        logger.error(f"Error testing flexibility scoring: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        # Clean up test data
        try:
            db.query(models.WorkoutScore).filter(models.WorkoutScore.user_id == test_user.id).delete()
            db.query(models.ExerciseSet).join(models.Exercise).join(models.Workout).filter(models.Workout.user_id == test_user.id).delete()
            db.query(models.Exercise).join(models.Workout).filter(models.Workout.user_id == test_user.id).delete()
            db.query(models.Workout).filter(models.Workout.user_id == test_user.id).delete()
            db.commit()
        except:
            db.rollback()
        db.close()

if __name__ == "__main__":
    test_flexibility_scoring()
