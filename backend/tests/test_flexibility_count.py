# backend/tests/test_flexibility_count.py
import sys
import os
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import models and database
from database import SessionLocal, engine
import models
from scoring_service import ScoringService

def create_test_workout(db, user_id, exercise_types):
    """
    Create a test workout with the specified exercise types
    
    Args:
        db: Database session
        user_id: User ID to create the workout for
        exercise_types: List of exercise types (e.g., ["strength", "flexibility"])
        
    Returns:
        The created workout
    """
    # Create a new workout
    workout = models.Workout(
        user_id=user_id,
        date=datetime.utcnow(),
        description="Test workout",
        status=models.WorkoutStatus.completed
    )
    db.add(workout)
    db.flush()
    
    # Add exercises
    for i, exercise_type in enumerate(exercise_types):
        exercise = models.Exercise(
            workout_id=workout.id,
            name=f"Test Exercise {i+1}",
            exercise_type=exercise_type
        )
        db.add(exercise)
        
        # Add a set to make it a valid exercise
        exercise_set = models.ExerciseSet(
            exercise_id=exercise.id,
            reps=10,
            weight=50.0
        )
        db.add(exercise_set)
    
    db.commit()
    return workout

def test_flexibility_count():
    """Test the scoring with different exercise counts"""
    db = SessionLocal()
    try:
        # Get or create a test user
        test_user = db.query(models.User).filter(models.User.username == "test_user").first()
        if not test_user:
            test_user = models.User(
                username="test_user",
                hashed_password="test_password",
                is_active=True
            )
            db.add(test_user)
            db.commit()
        
        # Create scoring service
        scoring_service = ScoringService(db)
        
        # Test different exercise counts with all strength
        logger.info("Testing different exercise counts with all strength exercises:")
        
        # 3 strength exercises
        workout1 = create_test_workout(db, test_user.id, ["strength"] * 3)
        score1 = scoring_service.calculate_workout_score(workout1.id)
        logger.info(f"3 strength exercises: {score1['base_score']} base points, {score1['total_score']} total points")
        
        # 6 strength exercises
        workout2 = create_test_workout(db, test_user.id, ["strength"] * 6)
        score2 = scoring_service.calculate_workout_score(workout2.id)
        logger.info(f"6 strength exercises: {score2['base_score']} base points, {score2['total_score']} total points")
        
        # 10 strength exercises
        workout3 = create_test_workout(db, test_user.id, ["strength"] * 10)
        score3 = scoring_service.calculate_workout_score(workout3.id)
        logger.info(f"10 strength exercises: {score3['base_score']} base points, {score3['total_score']} total points")
        
        # Test different exercise counts with all flexibility
        logger.info("\nTesting different exercise counts with all flexibility exercises:")
        
        # 3 flexibility exercises
        workout4 = create_test_workout(db, test_user.id, ["flexibility"] * 3)
        score4 = scoring_service.calculate_workout_score(workout4.id)
        logger.info(f"3 flexibility exercises: {score4['base_score']} base points (original: {score4['type_modifier']['original_score']}), {score4['total_score']} total points")
        
        # 6 flexibility exercises
        workout5 = create_test_workout(db, test_user.id, ["flexibility"] * 6)
        score5 = scoring_service.calculate_workout_score(workout5.id)
        logger.info(f"6 flexibility exercises: {score5['base_score']} base points (original: {score5['type_modifier']['original_score']}), {score5['total_score']} total points")
        
        # 10 flexibility exercises
        workout6 = create_test_workout(db, test_user.id, ["flexibility"] * 10)
        score6 = scoring_service.calculate_workout_score(workout6.id)
        logger.info(f"10 flexibility exercises: {score6['base_score']} base points (original: {score6['type_modifier']['original_score']}), {score6['total_score']} total points")
        
        # Test mixed workouts with different counts
        logger.info("\nTesting mixed workouts with different counts:")
        
        # 6 exercises: 3 strength, 3 flexibility (50% flexibility)
        workout7 = create_test_workout(db, test_user.id, ["strength"] * 3 + ["flexibility"] * 3)
        score7 = scoring_service.calculate_workout_score(workout7.id)
        logger.info(f"6 exercises (50% flexibility): {score7['base_score']} base points (original: {score7['type_modifier']['original_score']}), {score7['total_score']} total points")
        
        # 10 exercises: 7 strength, 3 flexibility (30% flexibility)
        workout8 = create_test_workout(db, test_user.id, ["strength"] * 7 + ["flexibility"] * 3)
        score8 = scoring_service.calculate_workout_score(workout8.id)
        logger.info(f"10 exercises (30% flexibility): {score8['base_score']} base points (original: {score8['type_modifier']['original_score']}), {score8['total_score']} total points")
        
        logger.info("\nAll tests completed!")
        
    except Exception as e:
        logger.error(f"Error testing flexibility scoring: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        # Clean up test data
        try:
            db.query(models.WorkoutScore).filter(models.WorkoutScore.user_id == test_user.id).delete()
            db.query(models.ExerciseSet).join(models.Exercise).join(models.Workout).filter(models.Workout.user_id == test_user.id).delete()
            db.query(models.Exercise).join(models.Workout).filter(models.Workout.user_id == test_user.id).delete()
            db.query(models.Workout).filter(models.Workout.user_id == test_user.id).delete()
            db.commit()
        except:
            db.rollback()
        db.close()

if __name__ == "__main__":
    test_flexibility_count()
