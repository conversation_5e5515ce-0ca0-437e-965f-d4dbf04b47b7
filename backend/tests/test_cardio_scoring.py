# backend/tests/test_cardio_scoring.py
import sys
import os
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import models and database
from database import SessionLocal, engine
import models
from scoring_service import ScoringService

def create_test_user(db):
    """Create a test user for scoring tests"""
    user = models.User(
        username=f"test_user_{datetime.utcnow().timestamp()}",
        hashed_password="test_password"
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user

def create_test_workout(db, user_id, exercise_types, cardio_durations=None):
    """
    Create a test workout with exercises of specified types

    Args:
        db: Database session
        user_id: User ID to associate with the workout
        exercise_types: List of exercise types to include
        cardio_durations: Optional list of durations in seconds for cardio exercises

    Returns:
        The created workout
    """
    # Create workout
    workout = models.Workout(
        user_id=user_id,
        date=datetime.utcnow(),
        status=models.WorkoutStatus.completed
    )
    db.add(workout)
    db.flush()

    # Track cardio exercise index for durations
    cardio_index = 0

    # Add exercises
    for i, exercise_type in enumerate(exercise_types):
        exercise = models.Exercise(
            name=f"Test Exercise {i+1}",
            exercise_type=exercise_type,
            workout_id=workout.id
        )
        db.add(exercise)
        db.flush()  # Flush to get exercise ID

        # Add a set to each exercise
        if exercise_type == models.ExerciseType.cardio:
            # Use specified duration if provided, otherwise default to 5 minutes (300 seconds)
            duration = 300
            if cardio_durations and cardio_index < len(cardio_durations):
                duration = cardio_durations[cardio_index]
                cardio_index += 1

            exercise_set = models.ExerciseSet(
                reps=None,
                weight=None,
                duration_seconds=duration,
                distance=duration / 600,  # Simple calculation: 1 mile per 10 minutes
                exercise_id=exercise.id
            )
        else:
            exercise_set = models.ExerciseSet(
                reps=10 if exercise_type == models.ExerciseType.strength else None,
                weight=50.0 if exercise_type == models.ExerciseType.strength else None,
                duration_seconds=None,
                exercise_id=exercise.id
            )

        db.add(exercise_set)

    db.commit()
    db.refresh(workout)
    return workout

def test_cardio_scoring():
    """Test that cardio exercises receive a point boost based on duration"""
    db = SessionLocal()
    try:
        # Create test user
        user = create_test_user(db)

        # Create scoring service
        scoring_service = ScoringService(db)

        # Test 1: All strength exercises (baseline)
        strength_workout = create_test_workout(
            db,
            user.id,
            [models.ExerciseType.strength] * 5
        )
        strength_score = scoring_service.calculate_workout_score(strength_workout.id)
        logger.info(f"All strength workout score: {strength_score}")

        # Test 2: Short cardio workout (5 minutes total)
        short_cardio_workout = create_test_workout(
            db,
            user.id,
            [models.ExerciseType.cardio] * 1,
            [300]  # 5 minutes (300 seconds)
        )
        short_cardio_score = scoring_service.calculate_workout_score(short_cardio_workout.id)
        logger.info(f"Short cardio workout score: {short_cardio_score}")

        # Test 3: Medium cardio workout (15 minutes total)
        medium_cardio_workout = create_test_workout(
            db,
            user.id,
            [models.ExerciseType.cardio] * 1,
            [900]  # 15 minutes (900 seconds)
        )
        medium_cardio_score = scoring_service.calculate_workout_score(medium_cardio_workout.id)
        logger.info(f"Medium cardio workout score: {medium_cardio_score}")

        # Test 4: Long cardio workout (30 minutes total)
        long_cardio_workout = create_test_workout(
            db,
            user.id,
            [models.ExerciseType.cardio] * 1,
            [1800]  # 30 minutes (1800 seconds)
        )
        long_cardio_score = scoring_service.calculate_workout_score(long_cardio_workout.id)
        logger.info(f"Long cardio workout score: {long_cardio_score}")

        # Test 5: Very long cardio workout (60 minutes total)
        very_long_cardio_workout = create_test_workout(
            db,
            user.id,
            [models.ExerciseType.cardio] * 1,
            [3600]  # 60 minutes (3600 seconds)
        )
        very_long_cardio_score = scoring_service.calculate_workout_score(very_long_cardio_workout.id)
        logger.info(f"Very long cardio workout score: {very_long_cardio_score}")

        # Test 6: Multiple cardio exercises (total 30 minutes)
        multi_cardio_workout = create_test_workout(
            db,
            user.id,
            [models.ExerciseType.cardio] * 3,
            [600, 600, 600]  # 3 x 10 minutes (600 seconds each)
        )
        multi_cardio_score = scoring_service.calculate_workout_score(multi_cardio_workout.id)
        logger.info(f"Multiple cardio exercises workout score: {multi_cardio_score}")

        # Test 7: Mixed workout with cardio and strength
        mixed_workout = create_test_workout(
            db,
            user.id,
            [models.ExerciseType.strength] * 3 + [models.ExerciseType.cardio] * 2,
            [900, 900]  # 2 x 15 minutes (900 seconds each)
        )
        mixed_score = scoring_service.calculate_workout_score(mixed_workout.id)
        logger.info(f"Mixed workout score: {mixed_score}")

        # Test 8: Complex workout with flexibility
        complex_workout = create_test_workout(
            db,
            user.id,
            [models.ExerciseType.strength] * 2 +
            [models.ExerciseType.cardio] * 2 +
            [models.ExerciseType.flexibility] * 1,
            [600, 600]  # 2 x 10 minutes (600 seconds each)
        )
        complex_score = scoring_service.calculate_workout_score(complex_workout.id)
        logger.info(f"Complex workout score: {complex_score}")

        # Verify results
        logger.info("\nTest Results (Base Score / Total Score):")
        logger.info(f"All strength (5 exercises): {strength_score['base_score']} / {strength_score['total_score']}")
        logger.info(f"Short cardio (5 min): {short_cardio_score['base_score']} / {short_cardio_score['total_score']}")
        logger.info(f"Medium cardio (15 min): {medium_cardio_score['base_score']} / {medium_cardio_score['total_score']}")
        logger.info(f"Long cardio (30 min): {long_cardio_score['base_score']} / {long_cardio_score['total_score']}")
        logger.info(f"Very long cardio (60 min): {very_long_cardio_score['base_score']} / {very_long_cardio_score['total_score']}")
        logger.info(f"Multiple cardio exercises (30 min total): {multi_cardio_score['base_score']} / {multi_cardio_score['total_score']}")
        logger.info(f"Mixed workout (3 strength, 2 cardio): {mixed_score['base_score']} / {mixed_score['total_score']}")
        logger.info(f"Complex workout (2 strength, 2 cardio, 1 flexibility): {complex_score['base_score']} / {complex_score['total_score']}")

        # Check duration-based bonus points
        logger.info("\nCardio Bonus Points:")
        logger.info(f"Short cardio (5 min): +{short_cardio_score['details']['type_modifier'].get('cardio_bonus_points', 0)} points")
        logger.info(f"Medium cardio (15 min): +{medium_cardio_score['details']['type_modifier'].get('cardio_bonus_points', 0)} points")
        logger.info(f"Long cardio (30 min): +{long_cardio_score['details']['type_modifier'].get('cardio_bonus_points', 0)} points")
        logger.info(f"Very long cardio (60 min): +{very_long_cardio_score['details']['type_modifier'].get('cardio_bonus_points', 0)} points")
        logger.info(f"Multiple cardio (30 min total): +{multi_cardio_score['details']['type_modifier'].get('cardio_bonus_points', 0)} points")

        # Show score breakdown for each scenario
        logger.info("\nDetailed Score Breakdown:")
        logger.info("All strength workout (5 exercises):")
        logger.info(f"  Original base score: {strength_score['details']['type_modifier']['original_score']}")
        logger.info(f"  Modified base score: {strength_score['base_score']}")
        logger.info(f"  Streak bonus: {strength_score['streak_bonus']}")
        logger.info(f"  Variety bonus: {strength_score['variety_bonus']}")
        logger.info(f"  New exercise bonus: {strength_score['new_exercise_bonus']}")
        logger.info(f"  Total score: {strength_score['total_score']}")

        logger.info("\nLong cardio workout (30 min):")
        logger.info(f"  Original base score: {long_cardio_score['details']['type_modifier']['original_score']}")
        logger.info(f"  Cardio bonus: +{long_cardio_score['details']['type_modifier'].get('cardio_bonus_points', 0)} points")
        logger.info(f"  Modified base score: {long_cardio_score['base_score']}")
        logger.info(f"  Streak bonus: {long_cardio_score['streak_bonus']}")
        logger.info(f"  Variety bonus: {long_cardio_score['variety_bonus']}")
        logger.info(f"  New exercise bonus: {long_cardio_score['new_exercise_bonus']}")
        logger.info(f"  Total score: {long_cardio_score['total_score']}")

        logger.info("\nMixed workout (3 strength, 2 cardio):")
        logger.info(f"  Original base score: {mixed_score['details']['type_modifier']['original_score']}")
        logger.info(f"  Cardio bonus: +{mixed_score['details']['type_modifier'].get('cardio_bonus_points', 0)} points")
        logger.info(f"  Modified base score: {mixed_score['base_score']}")
        logger.info(f"  Streak bonus: {mixed_score['streak_bonus']}")
        logger.info(f"  Variety bonus: {mixed_score['variety_bonus']}")
        logger.info(f"  New exercise bonus: {mixed_score['new_exercise_bonus']}")
        logger.info(f"  Total score: {mixed_score['total_score']}")

        logger.info("\nComplex workout (2 strength, 2 cardio, 1 flexibility):")
        logger.info(f"  Original base score: {complex_score['details']['type_modifier']['original_score']}")
        logger.info(f"  Flexibility reduction: -{complex_score['details']['type_modifier'].get('flexibility_reduction_percentage', 0)}%")
        logger.info(f"  Cardio bonus: +{complex_score['details']['type_modifier'].get('cardio_bonus_points', 0)} points")
        logger.info(f"  Modified base score: {complex_score['base_score']}")
        logger.info(f"  Streak bonus: {complex_score['streak_bonus']}")
        logger.info(f"  Variety bonus: {complex_score['variety_bonus']}")
        logger.info(f"  New exercise bonus: {complex_score['new_exercise_bonus']}")
        logger.info(f"  Total score: {complex_score['total_score']}")

        # Check that longer cardio workouts get higher bonus points
        assert short_cardio_score['details']['type_modifier'].get('cardio_bonus_points', 0) < \
               medium_cardio_score['details']['type_modifier'].get('cardio_bonus_points', 0), \
               "Medium cardio should have higher bonus points than short cardio"

        assert medium_cardio_score['details']['type_modifier'].get('cardio_bonus_points', 0) < \
               long_cardio_score['details']['type_modifier'].get('cardio_bonus_points', 0), \
               "Long cardio should have higher bonus points than medium cardio"

        # Check that multiple cardio exercises are summed correctly
        assert multi_cardio_score['details']['type_modifier'].get('cardio_bonus_points', 0) == \
               long_cardio_score['details']['type_modifier'].get('cardio_bonus_points', 0), \
               "Multiple cardio exercises totaling 30 minutes should have same bonus points as one 30-minute exercise"

        # Check that complex workout applies both cardio bonus and flexibility reduction
        assert 'cardio_bonus_points' in complex_score['details']['type_modifier'], \
            "Complex workout should have cardio bonus points"
        assert 'flexibility_reduction_percentage' in complex_score['details']['type_modifier'], \
            "Complex workout should have flexibility reduction"

        logger.info("All tests passed!")

    except Exception as e:
        logger.error(f"Error in test: {str(e)}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    test_cardio_scoring()
