# backend/scoring_service.py
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from datetime import datetime, timedelta
import logging
import json
import models
from typing import List, Dict, Any, Optional, Set

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ScoringService:
    """Service for calculating workout scores and tracking user progress"""

    def __init__(self, db: Session):
        self.db = db

    def calculate_workout_score(self, workout_id: int) -> Dict[str, Any]:
        """
        Calculate the score for a completed workout based on:
        - Base points for number of exercises
        - Streak bonus
        - Variety bonus
        - New exercise bonus
        - Exercise type modifiers (reduced points for flexibility exercises)

        Returns a dictionary with the score details
        """
        logger.info(f"Calculating score for workout {workout_id}")

        # Get the workout
        workout = self.db.query(models.Workout).filter(models.Workout.id == workout_id).first()
        if not workout:
            logger.error(f"Workout {workout_id} not found")
            return None

        # Ensure workout is completed
        if workout.status != models.WorkoutStatus.completed:
            logger.warning(f"Cannot score workout {workout_id} - not completed")
            return None

        # Get user stats
        user_stats = self._get_or_create_user_stats(workout.user_id)

        # Calculate base score based on number of exercises
        exercise_count = len(workout.exercises)
        base_score = self._calculate_base_score(exercise_count)

        # Apply exercise type modifier
        base_score, type_modifier = self._apply_exercise_type_modifier(workout, base_score)

        # Calculate streak bonus
        streak_days, streak_bonus = self._calculate_streak_bonus(workout.user_id, workout.date)

        # Calculate variety bonus
        variety_bonus, exercise_types = self._calculate_variety_bonus(workout)

        # Calculate new exercise bonus
        new_exercise_bonus, new_exercises = self._calculate_new_exercise_bonus(workout)

        # Calculate total score
        total_score = base_score + streak_bonus + variety_bonus + new_exercise_bonus

        # Create score details
        score_details = {
            "exercise_count": exercise_count,
            "exercise_types": list(exercise_types),
            "new_exercises": list(new_exercises),
            "streak_days": streak_days,
            "type_modifier": type_modifier
        }

        # Create or update workout score
        score = self._create_or_update_workout_score(
            workout_id=workout.id,
            user_id=workout.user_id,
            base_score=base_score,
            streak_bonus=streak_bonus,
            variety_bonus=variety_bonus,
            new_exercise_bonus=new_exercise_bonus,
            total_score=total_score,
            details=score_details
        )

        # Update user stats
        self._update_user_stats(
            user_stats=user_stats,
            score=total_score,
            streak_days=streak_days,
            workout_date=workout.date,
            new_exercises=new_exercises
        )

        # Check and update milestones
        self._update_milestones(workout.user_id)

        # Check for achievements
        self._check_achievements(workout.user_id)

        return {
            "workout_id": workout.id,
            "base_score": base_score,
            "streak_bonus": streak_bonus,
            "variety_bonus": variety_bonus,
            "new_exercise_bonus": new_exercise_bonus,
            "total_score": total_score,
            "details": score_details,
            "type_modifier": type_modifier
        }

    def _calculate_base_score(self, exercise_count: int) -> int:
        """Calculate base score based on number of exercises"""
        if exercise_count <= 2:
            return 50
        elif exercise_count == 3:
            return 70
        elif exercise_count == 4:
            return 100
        elif exercise_count == 5:
            return 130
        else:
            # 130 points for 5 exercises + 20 points for each additional exercise
            return 130 + (exercise_count - 5) * 20

    def _apply_exercise_type_modifier(self, workout, base_score: int) -> tuple:
        """
        Apply modifiers based on exercise types in the workout:
        - Reduces points for flexibility exercises
        - Adds additional points for cardio exercises based on duration

        Returns (modified_base_score, modifier_details)
        """
        # Count exercises by type
        type_counts = {}
        for exercise in workout.exercises:
            exercise_type = exercise.exercise_type
            type_counts[exercise_type] = type_counts.get(exercise_type, 0) + 1

        # Get total exercises and counts by type
        total_exercises = len(workout.exercises)
        flexibility_count = type_counts.get(models.ExerciseType.flexibility, 0)
        cardio_count = type_counts.get(models.ExerciseType.cardio, 0)

        if total_exercises == 0:
            return base_score, {"applied": False, "reason": "No exercises"}

        # Calculate percentages
        flexibility_percentage = flexibility_count / total_exercises if total_exercises > 0 else 0

        # Start with the base score
        modified_score = base_score
        modifier_applied = False
        modifier_details = {
            "original_score": base_score,
            "total_exercises": total_exercises
        }

        # Apply flexibility reduction if applicable
        if flexibility_count > 0:
            # Reduce score based on percentage of flexibility exercises
            # 50% reduction for workouts that are all flexibility
            # Linear scale for mixed workouts
            reduction_factor = 0.5 * flexibility_percentage

            # Apply the reduction
            flexibility_modified_score = int(modified_score * (1 - reduction_factor))

            # Ensure minimum score (at least 50% of original)
            flexibility_modified_score = max(flexibility_modified_score, int(modified_score * 0.5))

            # Update the score and details
            modified_score = flexibility_modified_score
            modifier_applied = True
            modifier_details["flexibility_applied"] = True
            modifier_details["flexibility_count"] = flexibility_count
            modifier_details["flexibility_reduction_percentage"] = int(reduction_factor * 100)

        # Apply cardio bonus if applicable - based on duration
        if cardio_count > 0:
            # Calculate total cardio duration in seconds
            total_cardio_duration = 0
            cardio_exercises = []

            for exercise in workout.exercises:
                if exercise.exercise_type == models.ExerciseType.cardio:
                    exercise_duration = 0
                    for exercise_set in exercise.sets:
                        if exercise_set.duration_seconds:
                            exercise_duration += exercise_set.duration_seconds

                    if exercise_duration > 0:
                        cardio_exercises.append({
                            "name": exercise.name,
                            "duration": exercise_duration
                        })
                        total_cardio_duration += exercise_duration

            # Calculate cardio bonus points based on total duration with diminishing returns
            # Base thresholds (in minutes):
            # - 5 minutes: 100 points
            # - 15 minutes: 200 points
            # - 30 minutes: 300 points (to make total score ~350 with base score)
            # - 60+ minutes: 400 points (cap)

            # Convert seconds to minutes for calculation
            total_cardio_minutes = total_cardio_duration / 60

            # Calculate bonus points with diminishing returns
            if total_cardio_minutes <= 5:
                # Linear from 0-100 points for 0-5 minutes
                cardio_bonus = int(100 * (total_cardio_minutes / 5))
            elif total_cardio_minutes <= 15:
                # 100-200 points for 5-15 minutes
                cardio_bonus = int(100 + 100 * ((total_cardio_minutes - 5) / 10))
            elif total_cardio_minutes <= 30:
                # 200-300 points for 15-30 minutes
                cardio_bonus = int(200 + 100 * ((total_cardio_minutes - 15) / 15))
            elif total_cardio_minutes <= 60:
                # 300-400 points for 30-60 minutes
                cardio_bonus = int(300 + 100 * ((total_cardio_minutes - 30) / 30))
            else:
                # Cap at 400 points for 60+ minutes
                cardio_bonus = 400

            # Add the bonus to the score
            cardio_modified_score = modified_score + cardio_bonus

            # Update the score and details
            modified_score = cardio_modified_score
            modifier_applied = True
            modifier_details["cardio_applied"] = True
            modifier_details["cardio_count"] = cardio_count
            modifier_details["cardio_duration_seconds"] = total_cardio_duration
            modifier_details["cardio_duration_minutes"] = round(total_cardio_minutes, 1)
            modifier_details["cardio_bonus_points"] = cardio_bonus
            modifier_details["cardio_exercises"] = cardio_exercises

        # Update final details
        modifier_details["applied"] = modifier_applied
        modifier_details["modified_score"] = modified_score

        if not modifier_applied:
            modifier_details["reason"] = "No exercise type modifiers applied"

        return modified_score, modifier_details

    def _calculate_streak_bonus(self, user_id: int, workout_date: datetime) -> tuple:
        """
        Calculate streak bonus based on consecutive workout days
        Returns (streak_days, bonus_points)
        """
        # Get user's workouts in the last 30 days, ordered by date
        thirty_days_ago = workout_date - timedelta(days=30)
        recent_workouts = self.db.query(models.Workout).filter(
            models.Workout.user_id == user_id,
            models.Workout.status == models.WorkoutStatus.completed,
            models.Workout.date >= thirty_days_ago,
            models.Workout.date <= workout_date
        ).order_by(models.Workout.date.desc()).all()

        # If this is the first workout, streak is 1
        if not recent_workouts or len(recent_workouts) == 1:
            return 1, 0

        # Calculate streak by checking consecutive days
        streak = 1
        current_date = workout_date.date()

        for workout in recent_workouts[1:]:  # Skip the current workout
            workout_date = workout.date.date()
            if workout_date == current_date - timedelta(days=1):
                streak += 1
                current_date = workout_date
            elif workout_date == current_date:
                # Same day workout, don't count for streak
                continue
            else:
                # Streak broken
                break

        # Calculate bonus: +10 points per consecutive day (max 50)
        bonus = min(streak * 10, 50)
        return streak, bonus

    def _calculate_variety_bonus(self, workout) -> tuple:
        """
        Calculate variety bonus based on different exercise types
        Returns (bonus_points, set_of_exercise_types)
        """
        exercise_types = set()
        for exercise in workout.exercises:
            exercise_types.add(exercise.exercise_type)

        # +15 points for each different exercise type beyond the first
        variety_count = len(exercise_types) - 1
        bonus = max(0, variety_count * 15)

        return bonus, exercise_types

    def _calculate_new_exercise_bonus(self, workout) -> tuple:
        """
        Calculate bonus for exercises the user has never done before
        Returns (bonus_points, set_of_new_exercise_names)
        """
        # Get all exercises the user has done before this workout
        previous_exercises = self.db.query(models.Exercise.name).join(
            models.Workout
        ).filter(
            models.Workout.user_id == workout.user_id,
            models.Workout.id != workout.id
        ).distinct().all()

        previous_exercise_names = {ex[0] for ex in previous_exercises}
        new_exercises = set()

        # Check each exercise in this workout
        for exercise in workout.exercises:
            if exercise.name not in previous_exercise_names:
                new_exercises.add(exercise.name)

        # +20 points for each new exercise
        bonus = len(new_exercises) * 20

        return bonus, new_exercises

    def _get_or_create_user_stats(self, user_id: int) -> models.UserStat:
        """Get or create user stats record"""
        user_stats = self.db.query(models.UserStat).filter(
            models.UserStat.user_id == user_id
        ).first()

        if not user_stats:
            user_stats = models.UserStat(user_id=user_id)
            self.db.add(user_stats)
            self.db.flush()

        return user_stats

    def _create_or_update_workout_score(
        self,
        workout_id: int,
        user_id: int,
        base_score: int,
        streak_bonus: int,
        variety_bonus: int,
        new_exercise_bonus: int,
        total_score: int,
        details: Dict[str, Any]
    ) -> models.WorkoutScore:
        """Create or update a workout score record"""
        # Check if score already exists
        score = self.db.query(models.WorkoutScore).filter(
            models.WorkoutScore.workout_id == workout_id
        ).first()

        if score:
            # Update existing score
            score.base_score = base_score
            score.streak_bonus = streak_bonus
            score.variety_bonus = variety_bonus
            score.new_exercise_bonus = new_exercise_bonus
            score.total_score = total_score
            score.details = details
        else:
            # Create new score
            score = models.WorkoutScore(
                workout_id=workout_id,
                user_id=user_id,
                base_score=base_score,
                streak_bonus=streak_bonus,
                variety_bonus=variety_bonus,
                new_exercise_bonus=new_exercise_bonus,
                total_score=total_score,
                date=datetime.utcnow(),
                details=details
            )
            self.db.add(score)

        self.db.commit()
        return score

    def _update_user_stats(
        self,
        user_stats: models.UserStat,
        score: int,
        streak_days: int,
        workout_date: datetime,
        new_exercises: Set[str]
    ) -> None:
        """Update user statistics after a workout is scored"""
        # Update total score
        user_stats.total_score += score

        # Update streak
        user_stats.current_streak = streak_days
        user_stats.longest_streak = max(user_stats.longest_streak, streak_days)

        # Update total workouts
        user_stats.total_workouts += 1

        # Update unique exercises
        user_stats.unique_exercises += len(new_exercises)

        # Update last updated timestamp
        user_stats.last_updated = datetime.utcnow()

        self.db.commit()

    def _update_milestones(self, user_id: int) -> None:
        """Update user's progress on milestones"""
        # Get user stats
        user_stats = self.db.query(models.UserStat).filter(
            models.UserStat.user_id == user_id
        ).first()

        if not user_stats:
            logger.warning(f"No stats found for user {user_id} when updating milestones")
            return

        # Get all in-progress milestones for the user
        milestones = self.db.query(models.Milestone).filter(
            models.Milestone.user_id == user_id,
            models.Milestone.status == models.MilestoneStatus.in_progress
        ).all()

        # Get current date for date-based calculations
        current_date = datetime.utcnow().date()

        for milestone in milestones:
            # Update milestone based on type
            if milestone.milestone_type == models.MilestoneType.weekly_workouts:
                # Check if milestone is still within its week
                if milestone.end_date and milestone.end_date.date() < current_date:
                    # Milestone week has passed, reset or complete
                    if milestone.current_value >= milestone.target_value:
                        self._complete_milestone(milestone)
                    else:
                        # Reset for new week
                        milestone.start_date = datetime.utcnow()
                        milestone.end_date = datetime.utcnow() + timedelta(days=7)
                        milestone.current_value = 1  # Count current workout
                else:
                    # Still within week, increment
                    milestone.current_value += 1
                    if milestone.current_value >= milestone.target_value:
                        self._complete_milestone(milestone)

            elif milestone.milestone_type == models.MilestoneType.monthly_workouts:
                # Similar logic for monthly workouts
                if milestone.end_date and milestone.end_date.date() < current_date:
                    if milestone.current_value >= milestone.target_value:
                        self._complete_milestone(milestone)
                    else:
                        # Reset for new month
                        milestone.start_date = datetime.utcnow()
                        milestone.end_date = datetime.utcnow() + timedelta(days=30)
                        milestone.current_value = 1
                else:
                    milestone.current_value += 1
                    if milestone.current_value >= milestone.target_value:
                        self._complete_milestone(milestone)

            elif milestone.milestone_type == models.MilestoneType.yearly_workouts:
                # Similar logic for yearly workouts
                if milestone.end_date and milestone.end_date.date() < current_date:
                    if milestone.current_value >= milestone.target_value:
                        self._complete_milestone(milestone)
                    else:
                        # Reset for new year
                        milestone.start_date = datetime.utcnow()
                        milestone.end_date = datetime.utcnow() + timedelta(days=365)
                        milestone.current_value = 1
                else:
                    milestone.current_value += 1
                    if milestone.current_value >= milestone.target_value:
                        self._complete_milestone(milestone)

            elif milestone.milestone_type == models.MilestoneType.consecutive_days:
                # Update based on current streak
                milestone.current_value = user_stats.current_streak
                if milestone.current_value >= milestone.target_value:
                    self._complete_milestone(milestone)

            elif milestone.milestone_type == models.MilestoneType.exercise_variety:
                # Update based on unique exercises
                milestone.current_value = user_stats.unique_exercises
                if milestone.current_value >= milestone.target_value:
                    self._complete_milestone(milestone)

        self.db.commit()

    def _complete_milestone(self, milestone: models.Milestone) -> None:
        """Mark a milestone as completed and award points"""
        milestone.status = models.MilestoneStatus.completed
        milestone.completed_date = datetime.utcnow()

        # Award points based on milestone difficulty
        # Points are proportional to the target value
        milestone.points_awarded = milestone.target_value * 10

        # Update user's total score
        user_stats = self.db.query(models.UserStat).filter(
            models.UserStat.user_id == milestone.user_id
        ).first()

        if user_stats:
            user_stats.total_score += milestone.points_awarded

        logger.info(f"Completed milestone {milestone.id} for user {milestone.user_id}")

    def _check_achievements(self, user_id: int) -> None:
        """Check if user has earned any new achievements"""
        # Get user stats
        user_stats = self.db.query(models.UserStat).filter(
            models.UserStat.user_id == user_id
        ).first()

        if not user_stats:
            return

        # Get all achievements the user hasn't earned yet
        earned_achievement_ids = self.db.query(models.UserAchievement.achievement_id).filter(
            models.UserAchievement.user_id == user_id
        ).all()

        earned_ids = [a[0] for a in earned_achievement_ids]

        # Get all achievements
        achievements = self.db.query(models.Achievement).filter(
            ~models.Achievement.id.in_(earned_ids) if earned_ids else True
        ).all()

        # Check each achievement
        for achievement in achievements:
            # This is where you would implement logic to check if the user has earned
            # specific achievements based on their stats and workout history
            # For now, we'll just use placeholder logic

            # Example: Achievement for completing 10 workouts
            if achievement.name == "Workout Warrior" and user_stats.total_workouts >= 10:
                self._award_achievement(user_id, achievement.id)

            # Example: Achievement for 7-day streak
            elif achievement.name == "Consistency King" and user_stats.current_streak >= 7:
                self._award_achievement(user_id, achievement.id)

            # Example: Achievement for trying 20 different exercises
            elif achievement.name == "Exercise Explorer" and user_stats.unique_exercises >= 20:
                self._award_achievement(user_id, achievement.id)

            # Example: Achievement for reaching 1000 points
            elif achievement.name == "Point Collector" and user_stats.total_score >= 1000:
                self._award_achievement(user_id, achievement.id)

    def _award_achievement(self, user_id: int, achievement_id: int) -> None:
        """Award an achievement to a user"""
        # Check if user already has this achievement
        existing = self.db.query(models.UserAchievement).filter(
            models.UserAchievement.user_id == user_id,
            models.UserAchievement.achievement_id == achievement_id
        ).first()

        if existing:
            return

        # Get the achievement
        achievement = self.db.query(models.Achievement).filter(
            models.Achievement.id == achievement_id
        ).first()

        if not achievement:
            logger.warning(f"Achievement {achievement_id} not found")
            return

        # Create user achievement record
        user_achievement = models.UserAchievement(
            user_id=user_id,
            achievement_id=achievement_id,
            date_earned=datetime.utcnow()
        )
        self.db.add(user_achievement)

        # Add achievement points to user's score
        user_stats = self.db.query(models.UserStat).filter(
            models.UserStat.user_id == user_id
        ).first()

        if user_stats:
            user_stats.total_score += achievement.points

        self.db.commit()
        logger.info(f"Awarded achievement {achievement.name} to user {user_id}")

    def create_default_milestones(self, user_id: int) -> None:
        """Create default milestones for a new user"""
        # Check if user already has milestones
        existing_milestones = self.db.query(models.Milestone).filter(
            models.Milestone.user_id == user_id
        ).count()

        if existing_milestones > 0:
            logger.info(f"User {user_id} already has milestones")
            return

        # Current date
        now = datetime.utcnow()

        # Weekly workout goals
        weekly_milestone = models.Milestone(
            user_id=user_id,
            title="Weekly Warrior",
            description="Complete 3 workouts this week",
            milestone_type=models.MilestoneType.weekly_workouts,
            target_value=3,
            current_value=0,
            status=models.MilestoneStatus.in_progress,
            start_date=now,
            end_date=now + timedelta(days=7)
        )
        self.db.add(weekly_milestone)

        # Monthly workout goals
        monthly_milestone = models.Milestone(
            user_id=user_id,
            title="Monthly Master",
            description="Complete 12 workouts this month",
            milestone_type=models.MilestoneType.monthly_workouts,
            target_value=12,
            current_value=0,
            status=models.MilestoneStatus.in_progress,
            start_date=now,
            end_date=now + timedelta(days=30)
        )
        self.db.add(monthly_milestone)

        # Yearly workout goals
        yearly_milestone = models.Milestone(
            user_id=user_id,
            title="Yearly Champion",
            description="Complete 100 workouts this year",
            milestone_type=models.MilestoneType.yearly_workouts,
            target_value=100,
            current_value=0,
            status=models.MilestoneStatus.in_progress,
            start_date=now,
            end_date=now + timedelta(days=365)
        )
        self.db.add(yearly_milestone)

        # Streak milestone
        streak_milestone = models.Milestone(
            user_id=user_id,
            title="Streak Seeker",
            description="Workout for 7 consecutive days",
            milestone_type=models.MilestoneType.consecutive_days,
            target_value=7,
            current_value=0,
            status=models.MilestoneStatus.in_progress,
            start_date=now
        )
        self.db.add(streak_milestone)

        # Exercise variety milestone
        variety_milestone = models.Milestone(
            user_id=user_id,
            title="Exercise Explorer",
            description="Try 20 different exercises",
            milestone_type=models.MilestoneType.exercise_variety,
            target_value=20,
            current_value=0,
            status=models.MilestoneStatus.in_progress,
            start_date=now
        )
        self.db.add(variety_milestone)

        self.db.commit()
        logger.info(f"Created default milestones for user {user_id}")

    def create_default_achievements(self) -> None:
        """Create default achievements if they don't exist"""
        # Check if achievements already exist
        achievement_count = self.db.query(models.Achievement).count()

        if achievement_count > 0:
            logger.info("Achievements already exist")
            return

        # Create default achievements
        achievements = [
            models.Achievement(
                name="Workout Warrior",
                description="Complete 10 workouts",
                icon="trophy",
                points=100
            ),
            models.Achievement(
                name="Consistency King",
                description="Maintain a 7-day workout streak",
                icon="calendar-check",
                points=150
            ),
            models.Achievement(
                name="Exercise Explorer",
                description="Try 20 different exercises",
                icon="dumbbell",
                points=200
            ),
            models.Achievement(
                name="Point Collector",
                description="Earn 1000 total points",
                icon="star",
                points=250
            ),
            models.Achievement(
                name="Strength Specialist",
                description="Complete 20 strength workouts",
                icon="weight",
                points=150
            ),
            models.Achievement(
                name="Cardio Champion",
                description="Complete 20 cardio workouts",
                icon="heartbeat",
                points=150
            ),
            models.Achievement(
                name="Flexibility Fan",
                description="Complete 20 flexibility workouts",
                icon="child",
                points=150
            ),
            models.Achievement(
                name="Milestone Master",
                description="Complete 5 milestones",
                icon="flag-checkered",
                points=300
            )
        ]

        for achievement in achievements:
            self.db.add(achievement)

        self.db.commit()
        logger.info("Created default achievements")
