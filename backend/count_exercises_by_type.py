# backend/count_exercises_by_type.py
import sys
import os
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import models and database
from database import SessionLocal, engine
import models

def count_exercises_by_type():
    """Count exercises by type in the exercise library"""
    db = SessionLocal()
    try:
        # Count exercises by type
        strength_count = db.query(models.ExerciseLibrary).filter(
            models.ExerciseLibrary.exercise_type == models.ExerciseType.strength
        ).count()
        
        cardio_count = db.query(models.ExerciseLibrary).filter(
            models.ExerciseLibrary.exercise_type == models.ExerciseType.cardio
        ).count()
        
        flexibility_count = db.query(models.ExerciseLibrary).filter(
            models.ExerciseLibrary.exercise_type == models.ExerciseType.flexibility
        ).count()
        
        other_count = db.query(models.ExerciseLibrary).filter(
            models.ExerciseLibrary.exercise_type == models.ExerciseType.other
        ).count()
        
        total_count = db.query(models.ExerciseLibrary).count()
        
        # Print the results
        logger.info("Exercise counts by type:")
        logger.info(f"Strength: {strength_count}")
        logger.info(f"Cardio: {cardio_count}")
        logger.info(f"Flexibility: {flexibility_count}")
        logger.info(f"Other: {other_count}")
        logger.info(f"Total: {total_count}")
        
        return {
            "strength": strength_count,
            "cardio": cardio_count,
            "flexibility": flexibility_count,
            "other": other_count,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error counting exercises: {str(e)}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    count_exercises_by_type()
