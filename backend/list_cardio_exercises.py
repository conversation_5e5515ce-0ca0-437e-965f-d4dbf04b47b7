# backend/list_cardio_exercises.py
import sys
import os
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import models and database
from database import SessionLocal, engine
import models

def list_cardio_exercises():
    """List all cardio exercises in the exercise library"""
    db = SessionLocal()
    try:
        # Query all cardio exercises
        cardio_exercises = db.query(models.ExerciseLibrary).filter(
            models.ExerciseLibrary.exercise_type == models.ExerciseType.cardio
        ).order_by(models.ExerciseLibrary.name).all()
        
        # Print the results
        logger.info(f"Found {len(cardio_exercises)} cardio exercises:")
        for i, exercise in enumerate(cardio_exercises, 1):
            # Get associated equipment
            equipment = [eq.name for eq in exercise.equipment]
            equipment_str = ", ".join(equipment) if equipment else "None"
            
            # Get associated muscle groups
            muscle_groups = [mg.name for mg in exercise.muscle_groups]
            muscle_groups_str = ", ".join(muscle_groups) if muscle_groups else "None"
            
            logger.info(f"{i}. {exercise.name}")
            logger.info(f"   Description: {exercise.description or 'None'}")
            logger.info(f"   Difficulty: {exercise.difficulty or 'Not specified'}")
            logger.info(f"   Equipment: {equipment_str}")
            logger.info(f"   Muscle Groups: {muscle_groups_str}")
            logger.info("---")
        
        return cardio_exercises
    except Exception as e:
        logger.error(f"Error listing cardio exercises: {str(e)}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    list_cardio_exercises()
