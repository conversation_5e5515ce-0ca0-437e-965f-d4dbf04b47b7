#!/bin/bash

# add_points.sh - <PERSON><PERSON><PERSON> to add points to a user in the workout tracker app

# Check if required arguments are provided
if [ $# -lt 2 ]; then
    echo "Usage: $0 <username> <points>"
    echo "Example: $0 john_doe 500"
    exit 1
fi

USERNAME="$1"
POINTS="$2"

# Get the container name for the backend
BACKEND_CONTAINER=$(docker compose ps -q backend)

if [ -z "$BACKEND_CONTAINER" ]; then
    echo "Error: Backend container not found. Make sure docker compose is running."
    exit 1
fi

echo "Found backend container: $BACKEND_CONTAINER"

# Make sure the script is executable in the container
echo "Copying script to container and making it executable..."
docker cp backend/add_user_points.py $BACKEND_CONTAINER:/app/
docker exec $BACKEND_CONTAINER chmod +x /app/add_user_points.py

# Execute the Python script in the container
echo "Adding $POINTS points to user '$USERNAME'..."
docker exec $BACKEND_CONTAINER python /app/add_user_points.py "$USERNAME" "$POINTS"

# Check if the command was successful
if [ $? -eq 0 ]; then
    echo "Success! $POINTS points have been added to user '$USERNAME'."
else
    echo "Error: Failed to add points. Check the error message above."
    exit 1
fi

echo "Done!"
