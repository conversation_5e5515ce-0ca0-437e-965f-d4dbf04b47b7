#!/bin/bash

# Get the container name for the database
DB_CONTAINER=$(docker-compose ps -q db)

if [ -z "$DB_CONTAINER" ]; then
    echo "Error: Database container not found. Make sure docker-compose is running."
    exit 1
fi

echo "Found database container: $DB_CONTAINER"

# Copy the SQL file to the container
echo "Copying SQL file to container..."
docker cp add_form_analysis_tables.sql $DB_CONTAINER:/tmp/

# Execute the SQL file
echo "Executing SQL script..."
docker-compose exec db psql -U postgres -d workout_db -f /tmp/add_form_analysis_tables.sql

# Check if the tables were created
echo "Verifying tables were created..."
docker-compose exec db psql -U postgres -d workout_db -c "\dt form_*"

echo "Done! The database should now have the required tables for form analysis."
echo "Restart the backend container with: docker-compose restart backend"
