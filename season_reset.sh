#!/bin/bash

# Simple Season Reset Script
# Resets all points, achievements, and scoring data while preserving everything else

# Database connection settings
DB_HOST=${POSTGRES_HOST:-db}
DB_PORT=${POSTGRES_PORT:-5432}
DB_NAME=${POSTGRES_DB:-workout_db}
DB_USER=${POSTGRES_USER:-postgres}
DB_PASSWORD=${POSTGRES_PASSWORD:-postgres}

echo "=========================================="
echo "    Workout Tracker Season Reset"
echo "=========================================="
echo ""
echo "This will reset:"
echo "- All workout scores"
echo "- All user achievements"
echo "- All milestones"
echo "- All user stats (points, streaks, etc.)"
echo ""
echo "This will PRESERVE:"
echo "- User accounts"
echo "- Workout history"
echo "- Exercise data"
echo "- Weight tracking data"
echo ""

read -p "Are you sure you want to proceed? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "Season reset cancelled."
    exit 0
fi

echo ""
echo "Resetting season data..."

# SQL to reset all scoring data
RESET_SQL="
BEGIN;

-- Delete all workout scores
DELETE FROM workout_scores;

-- Delete all user achievements
DELETE FROM user_achievements;

-- Delete all milestones
DELETE FROM milestones;

-- Reset all user stats to zero
UPDATE user_stats SET 
    total_score = 0,
    current_streak = 0,
    longest_streak = 0,
    total_workouts = 0,
    unique_exercises = 0,
    last_updated = NOW();

COMMIT;
"

# Execute the reset using Docker
if docker compose exec db psql -U $DB_USER -d $DB_NAME -c "$RESET_SQL"; then
    echo ""
    echo "✅ Season reset completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. docker compose exec backend python seed_scoring.py"
    echo "2. docker compose exec backend python calculate_existing_scores.py"
else
    echo ""
    echo "❌ Season reset failed!"
    exit 1
fi
