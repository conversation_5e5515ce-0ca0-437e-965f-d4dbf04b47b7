#!/bin/bash

# Season Reset Script for Workout Tracker
# This script resets all points, achievements, and scoring data while preserving
# user accounts, workout history, and other core data.

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get environment variables
DB_HOST=${POSTGRES_HOST:-db}
DB_PORT=${POSTGRES_PORT:-5432}
DB_NAME=${POSTGRES_DB:-workout_db}
DB_USER=${POSTGRES_USER:-postgres}
DB_PASSWORD=${POSTGRES_PASSWORD:-postgres}

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to execute SQL and check result
execute_sql() {
    local sql_command="$1"
    local description="$2"
    
    print_status "$description"
    if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "$sql_command" > /dev/null 2>&1; then
        print_success "$description completed"
        return 0
    else
        print_error "$description failed"
        return 1
    fi
}

# Function to get count from table
get_count() {
    local table_name="$1"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM $table_name;" | tr -d ' '
}

# Function to create backup
create_backup() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_dir="database_backups/season_reset_$timestamp"
    
    print_status "Creating backup before season reset..."
    mkdir -p "$backup_dir"
    
    # Backup scoring-related tables
    local tables=("workout_scores" "user_achievements" "milestones" "user_stats")
    
    for table in "${tables[@]}"; do
        print_status "Backing up table: $table"
        PGPASSWORD=$DB_PASSWORD pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t $table --data-only > "$backup_dir/${table}_backup.sql"
        if [ $? -eq 0 ]; then
            print_success "Backup created: $backup_dir/${table}_backup.sql"
        else
            print_error "Failed to backup table: $table"
            return 1
        fi
    done
    
    # Create a restore script
    cat > "$backup_dir/restore_season_data.sh" << 'EOF'
#!/bin/bash
# Restore script for season data
# Run this script to restore the backed up season data

BACKUP_DIR=$(dirname "$0")
DB_HOST=${POSTGRES_HOST:-db}
DB_PORT=${POSTGRES_PORT:-5432}
DB_NAME=${POSTGRES_DB:-workout_db}
DB_USER=${POSTGRES_USER:-postgres}
DB_PASSWORD=${POSTGRES_PASSWORD:-postgres}

echo "Restoring season data from backup..."

# Restore each table
for backup_file in "$BACKUP_DIR"/*_backup.sql; do
    if [ -f "$backup_file" ]; then
        echo "Restoring $(basename "$backup_file")"
        PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f "$backup_file"
    fi
done

echo "Season data restore completed!"
EOF
    
    chmod +x "$backup_dir/restore_season_data.sh"
    print_success "Backup completed in: $backup_dir"
    print_success "Restore script created: $backup_dir/restore_season_data.sh"
    
    echo "$backup_dir"
}

# Function to show current stats
show_current_stats() {
    print_status "Current database statistics:"
    
    local workout_scores_count=$(get_count "workout_scores")
    local user_achievements_count=$(get_count "user_achievements")
    local milestones_count=$(get_count "milestones")
    local user_stats_count=$(get_count "user_stats")
    local users_count=$(get_count "users")
    local workouts_count=$(get_count "workouts")
    
    echo "  Users: $users_count"
    echo "  Workouts: $workouts_count"
    echo "  Workout Scores: $workout_scores_count"
    echo "  User Achievements: $user_achievements_count"
    echo "  Milestones: $milestones_count"
    echo "  User Stats: $user_stats_count"
}

# Function to reset season data
reset_season_data() {
    print_status "Starting season reset..."
    
    # SQL commands to reset season data
    local reset_sql=$(cat <<'EOF'
-- Reset Season Data
-- This will delete all scoring, achievements, and milestone data
-- while preserving user accounts, workouts, and exercise data

BEGIN;

-- Delete workout scores
DELETE FROM workout_scores;

-- Delete user achievements (earned achievements)
DELETE FROM user_achievements;

-- Delete milestones
DELETE FROM milestones;

-- Reset user stats
UPDATE user_stats SET 
    total_score = 0,
    current_streak = 0,
    longest_streak = 0,
    total_workouts = 0,
    unique_exercises = 0,
    last_updated = NOW();

COMMIT;
EOF
)
    
    print_status "Executing season reset SQL..."
    if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "$reset_sql"; then
        print_success "Season reset completed successfully!"
        return 0
    else
        print_error "Season reset failed!"
        return 1
    fi
}

# Function to verify reset
verify_reset() {
    print_status "Verifying season reset..."
    
    local workout_scores_count=$(get_count "workout_scores")
    local user_achievements_count=$(get_count "user_achievements")
    local milestones_count=$(get_count "milestones")
    
    # Check user_stats are reset
    local non_zero_scores=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM user_stats WHERE total_score > 0 OR current_streak > 0 OR longest_streak > 0;" | tr -d ' ')
    
    if [ "$workout_scores_count" -eq 0 ] && [ "$user_achievements_count" -eq 0 ] && [ "$milestones_count" -eq 0 ] && [ "$non_zero_scores" -eq 0 ]; then
        print_success "Season reset verification passed!"
        print_success "All scoring data has been successfully reset"
        return 0
    else
        print_error "Season reset verification failed!"
        echo "  Workout Scores remaining: $workout_scores_count"
        echo "  User Achievements remaining: $user_achievements_count"
        echo "  Milestones remaining: $milestones_count"
        echo "  Non-zero user stats: $non_zero_scores"
        return 1
    fi
}

# Main execution
main() {
    echo "=========================================="
    echo "    Workout Tracker Season Reset"
    echo "=========================================="
    echo ""
    
    print_status "Connecting to PostgreSQL at $DB_HOST:$DB_PORT"
    
    # Test database connection
    if ! PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" > /dev/null 2>&1; then
        print_error "Cannot connect to database. Please check your connection settings."
        exit 1
    fi
    
    print_success "Database connection successful"
    echo ""
    
    # Show current stats
    show_current_stats
    echo ""
    
    # Confirmation prompt
    print_warning "This will reset ALL points, achievements, and scoring data!"
    print_warning "User accounts, workouts, and exercise data will be preserved."
    echo ""
    read -p "Are you sure you want to proceed? (yes/no): " confirm
    
    if [ "$confirm" != "yes" ]; then
        print_status "Season reset cancelled by user"
        exit 0
    fi
    
    echo ""
    
    # Create backup
    backup_dir=$(create_backup)
    if [ $? -ne 0 ]; then
        print_error "Backup failed. Aborting season reset."
        exit 1
    fi
    
    echo ""
    
    # Reset season data
    if reset_season_data; then
        echo ""
        verify_reset
        if [ $? -eq 0 ]; then
            echo ""
            print_success "=========================================="
            print_success "    Season Reset Completed Successfully!"
            print_success "=========================================="
            echo ""
            print_status "Backup location: $backup_dir"
            print_status "To restore data if needed, run: $backup_dir/restore_season_data.sh"
            echo ""
            print_status "You may now want to run the scoring initialization scripts:"
            print_status "  1. docker-compose exec backend python seed_scoring.py"
            print_status "  2. docker-compose exec backend python calculate_existing_scores.py"
        else
            print_error "Season reset verification failed. Please check the database manually."
            exit 1
        fi
    else
        print_error "Season reset failed. Database may be in an inconsistent state."
        print_status "You can restore from backup using: $backup_dir/restore_season_data.sh"
        exit 1
    fi
}

# Run main function
main "$@"
