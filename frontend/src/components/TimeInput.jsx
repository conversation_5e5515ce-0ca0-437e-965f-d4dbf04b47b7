// frontend/src/components/TimeInput.jsx
import React, { useState, useEffect } from 'react';

/**
 * A component for inputting time in hours, minutes, and seconds format
 * that converts to total seconds for storage.
 */
const TimeInput = ({ value, onChange, placeholder }) => {
  // Split the total seconds into hours, minutes, seconds
  const [hours, setHours] = useState(0);
  const [minutes, setMinutes] = useState(0);
  const [seconds, setSeconds] = useState(0);

  // When the external value changes, update our internal state
  useEffect(() => {
    if (value) {
      const totalSeconds = parseInt(value, 10);
      const hrs = Math.floor(totalSeconds / 3600);
      const mins = Math.floor((totalSeconds % 3600) / 60);
      const secs = totalSeconds % 60;
      
      setHours(hrs);
      setMinutes(mins);
      setSeconds(secs);
    } else {
      // Reset if value is empty
      setHours(0);
      setMinutes(0);
      setSeconds(0);
    }
  }, [value]);

  // When any of our internal values change, update the external value
  const handleChange = (field, val) => {
    let h = hours;
    let m = minutes;
    let s = seconds;

    // Update the appropriate field
    if (field === 'hours') {
      h = val === '' ? 0 : parseInt(val, 10);
      setHours(h);
    } else if (field === 'minutes') {
      m = val === '' ? 0 : parseInt(val, 10);
      // Ensure minutes are between 0-59
      if (m > 59) m = 59;
      setMinutes(m);
    } else if (field === 'seconds') {
      s = val === '' ? 0 : parseInt(val, 10);
      // Ensure seconds are between 0-59
      if (s > 59) s = 59;
      setSeconds(s);
    }

    // Calculate total seconds and call the onChange handler
    const totalSeconds = h * 3600 + m * 60 + s;
    onChange(totalSeconds.toString());
  };

  return (
    <div className="flex space-x-2">
      <div className="flex-1">
        <input
          type="number"
          min="0"
          value={hours || ''}
          onChange={(e) => handleChange('hours', e.target.value)}
          className="w-full border border-gray-300 rounded-md p-2 text-center"
          placeholder="HH"
          aria-label="Hours"
        />
        <div className="text-xs text-center text-gray-500 mt-1">Hours</div>
      </div>
      <div className="flex items-center font-bold text-gray-400">:</div>
      <div className="flex-1">
        <input
          type="number"
          min="0"
          max="59"
          value={minutes || ''}
          onChange={(e) => handleChange('minutes', e.target.value)}
          className="w-full border border-gray-300 rounded-md p-2 text-center"
          placeholder="MM"
          aria-label="Minutes"
        />
        <div className="text-xs text-center text-gray-500 mt-1">Minutes</div>
      </div>
      <div className="flex items-center font-bold text-gray-400">:</div>
      <div className="flex-1">
        <input
          type="number"
          min="0"
          max="59"
          value={seconds || ''}
          onChange={(e) => handleChange('seconds', e.target.value)}
          className="w-full border border-gray-300 rounded-md p-2 text-center"
          placeholder="SS"
          aria-label="Seconds"
        />
        <div className="text-xs text-center text-gray-500 mt-1">Seconds</div>
      </div>
    </div>
  );
};

export default TimeInput;
