// frontend/src/components/WorkoutVolumeChart.jsx
import React, { useState, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import { format, subMonths } from 'date-fns';
import ResponsiveChartContainer from './ResponsiveChartContainer';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const WorkoutVolumeChart = ({ workouts }) => {
  const [chartData, setChartData] = useState(null);
  const [timeRange, setTimeRange] = useState('3months'); // Default to 3 months

  // Calculate total volume for each workout
  useEffect(() => {
    if (!workouts || workouts.length === 0) return;

    // Filter workouts based on selected time range
    const cutoffDate = new Date();
    switch (timeRange) {
      case '1month':
        cutoffDate.setMonth(cutoffDate.getMonth() - 1);
        break;
      case '3months':
        cutoffDate.setMonth(cutoffDate.getMonth() - 3);
        break;
      case '6months':
        cutoffDate.setMonth(cutoffDate.getMonth() - 6);
        break;
      case '1year':
        cutoffDate.setFullYear(cutoffDate.getFullYear() - 1);
        break;
      default:
        cutoffDate.setMonth(cutoffDate.getMonth() - 3);
    }

    // Filter workouts by date and calculate volume
    const filteredWorkouts = workouts
      .filter(workout => new Date(workout.date) >= cutoffDate)
      .map(workout => {
        // Calculate total volume for this workout (weight × reps)
        let totalVolume = 0;

        workout.exercises.forEach(exercise => {
          if (exercise.exercise_type === 'strength') {
            exercise.sets.forEach(set => {
              if (set.weight && set.reps) {
                totalVolume += set.weight * set.reps;
              }
            });
          }
        });

        return {
          date: new Date(workout.date),
          totalVolume,
          id: workout.id
        };
      })
      .filter(workout => workout.totalVolume > 0) // Only include workouts with volume
      .sort((a, b) => a.date - b.date); // Sort by date

    if (filteredWorkouts.length > 0) {
      // Prepare data for Chart.js
      const data = {
        labels: filteredWorkouts.map(workout => format(workout.date, 'MMM d')),
        datasets: [
          {
            label: 'Total Volume (lbs)',
            data: filteredWorkouts.map(workout => workout.totalVolume),
            backgroundColor: 'rgba(99, 102, 241, 0.7)',
            borderColor: 'rgb(99, 102, 241)',
            borderWidth: 1
          }
        ]
      };

      setChartData(data);
    } else {
      setChartData(null);
    }
  }, [workouts, timeRange]);

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 10,
          font: {
            size: 10
          }
        }
      },
      title: {
        display: true,
        text: 'Workout Volume Over Time',
        font: {
          size: 14
        }
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            return `Volume: ${Math.round(context.parsed.y).toLocaleString()} lbs`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Volume (Weight × Reps)',
          font: {
            size: 10
          }
        },
        ticks: {
          font: {
            size: 9
          }
        }
      },
      x: {
        title: {
          display: true,
          text: 'Workout Date',
          font: {
            size: 10
          }
        },
        ticks: {
          maxRotation: 90,
          minRotation: 45,
          font: {
            size: 8
          }
        }
      }
    }
  };

  return (
    <div className="bg-white shadow-sm rounded-lg p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Workout Volume Tracker</h2>

        <div className="flex space-x-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          >
            <option value="1month">Last Month</option>
            <option value="3months">Last 3 Months</option>
            <option value="6months">Last 6 Months</option>
            <option value="1year">Last Year</option>
          </select>
        </div>
      </div>

      {chartData ? (
        <ResponsiveChartContainer>
          <Bar data={chartData} options={chartOptions} />
        </ResponsiveChartContainer>
      ) : (
        <ResponsiveChartContainer className="flex items-center justify-center bg-gray-50 rounded-lg">
          <p className="text-gray-500">No volume data available for the selected time period</p>
        </ResponsiveChartContainer>
      )}

      <div className="mt-4 text-sm text-gray-500">
        <p>Volume = Weight × Reps (total across all sets and exercises)</p>
      </div>
    </div>
  );
};

export default WorkoutVolumeChart;
