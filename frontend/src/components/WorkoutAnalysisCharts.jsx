import React, { useState, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  ArcElement,
  RadialLinearScale,
  Filler,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { Bar, Pie, Radar } from 'react-chartjs-2';
import ResponsiveChartContainer from './ResponsiveChartContainer';
import api from '../services/api';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  ArcElement,
  RadialLinearScale,
  Filler,
  Title,
  Tooltip,
  Legend
);

// Chart colors
const CHART_COLORS = {
  primary: 'rgb(99, 102, 241)', // indigo-500
  primaryLight: 'rgba(99, 102, 241, 0.5)',
  secondary: 'rgb(14, 165, 233)', // sky-500
  secondaryLight: 'rgba(14, 165, 233, 0.5)',
  highlight: 'rgb(249, 115, 22)', // orange-500
  highlightLight: 'rgba(249, 115, 22, 0.7)',
  success: 'rgb(34, 197, 94)', // green-500
  successLight: 'rgba(34, 197, 94, 0.5)',
  danger: 'rgb(239, 68, 68)', // red-500
  dangerLight: 'rgba(239, 68, 68, 0.5)',
  neutral: 'rgb(107, 114, 128)', // gray-500
  neutralLight: 'rgba(107, 114, 128, 0.5)'
};

// Pie chart colors
const PIE_COLORS = [
  CHART_COLORS.primary,
  CHART_COLORS.secondary,
  CHART_COLORS.success,
  CHART_COLORS.highlight,
  CHART_COLORS.danger,
  CHART_COLORS.neutral,
  'rgb(168, 85, 247)', // purple-500
  'rgb(236, 72, 153)'  // pink-500
];

// Common muscle groups for strength training
const MUSCLE_GROUPS = [
  'chest',
  'back',
  'shoulders',
  'biceps',
  'triceps',
  'legs',
  'core',
  'glutes',
  'hamstrings',
  'quadriceps',
  'calves',
  'forearms'
];

// Display names for muscle groups (capitalized)
const MUSCLE_GROUP_DISPLAY_NAMES = MUSCLE_GROUPS.map(
  group => group.charAt(0).toUpperCase() + group.slice(1)
);

/**
 * Component to display workout analysis charts based on processed data
 */
const WorkoutAnalysisCharts = ({ processedData }) => {
  const [exerciseMuscleMap, setExerciseMuscleMap] = useState({});
  const [muscleGroupChartData, setMuscleGroupChartData] = useState(null);
  const [loading, setLoading] = useState(true);

  // Extract data safely
  const exercise_stats = processedData?.exercise_stats || [];
  const workout_stats = processedData?.workout_stats || {};
  const hasData = processedData && processedData.exercise_stats && processedData.workout_stats;

  // Fetch exercise-muscle group mappings from the API
  useEffect(() => {
    const fetchMuscleGroupMappings = async () => {
      try {
        setLoading(true);
        const mappings = await api.getExerciseMuscleGroupMappings();

        // Convert the array of mappings to a lookup object
        // Each exercise can map to multiple muscle groups
        const muscleMap = {};
        mappings.forEach(mapping => {
          muscleMap[mapping.exercise_name.toLowerCase()] = mapping.muscle_groups.map(mg => mg.toLowerCase());
        });

        setExerciseMuscleMap(muscleMap);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching muscle group mappings:', err);
        setLoading(false);
      }
    };

    fetchMuscleGroupMappings();
  }, []);

  // Process muscle group data for the current workout
  useEffect(() => {
    if (!hasData || loading || Object.keys(exerciseMuscleMap).length === 0) return;

    // Count frequency of each muscle group in the current workout
    const muscleGroupCounts = {};

    // Initialize all muscle groups to 0
    Object.keys(exerciseMuscleMap).forEach(exerciseName => {
      const muscleGroups = exerciseMuscleMap[exerciseName];
      if (muscleGroups && Array.isArray(muscleGroups)) {
        muscleGroups.forEach(group => {
          if (group) {
            const lowerGroup = group.toLowerCase();
            muscleGroupCounts[lowerGroup] = muscleGroupCounts[lowerGroup] || 0;
          }
        });
      }
    });

    // Also initialize our predefined muscle groups
    MUSCLE_GROUPS.forEach(group => {
      muscleGroupCounts[group] = muscleGroupCounts[group] || 0;
    });

    // Process each exercise in the current workout
    exercise_stats.forEach(exercise => {
      // Try to find the exercise in our mapping (case-insensitive)
      const exerciseName = exercise.name.toLowerCase();

      // Try exact match first
      let muscleGroups = exerciseMuscleMap[exerciseName];

      // If no exact match, try case-insensitive match
      if (!muscleGroups) {
        const matchingKey = Object.keys(exerciseMuscleMap).find(key =>
          key.toLowerCase() === exerciseName
        );

        if (matchingKey) {
          muscleGroups = exerciseMuscleMap[matchingKey];
        } else {
          // Try partial matching as a last resort
          const matchingKeys = Object.keys(exerciseMuscleMap).filter(key =>
            key.toLowerCase().includes(exerciseName) ||
            exerciseName.includes(key.toLowerCase())
          );

          if (matchingKeys.length > 0) {
            // Use the first match
            muscleGroups = exerciseMuscleMap[matchingKeys[0]];
          }
        }
      }

      // If we found muscle groups, increment their counts
      if (muscleGroups && Array.isArray(muscleGroups)) {
        muscleGroups.forEach(group => {
          if (group) {
            const lowerGroup = group.toLowerCase();
            if (muscleGroupCounts.hasOwnProperty(lowerGroup)) {
              muscleGroupCounts[lowerGroup] += 1;
            }
          }
        });
      }
    });

    // Prepare data for Chart.js - use only muscle groups that have data
    const activeGroups = Object.keys(muscleGroupCounts).filter(group =>
      muscleGroupCounts[group] > 0
    );

    // Sort by count (descending)
    const sortedGroups = [...activeGroups].sort((a, b) =>
      muscleGroupCounts[b] - muscleGroupCounts[a]
    );

    const sortedGroupNames = sortedGroups.map(
      group => group.charAt(0).toUpperCase() + group.slice(1)
    );

    if (sortedGroups.length > 0) {
      const data = {
        labels: sortedGroupNames,
        datasets: [
          {
            label: 'Exercises Per Muscle Group',
            data: sortedGroups.map(group => muscleGroupCounts[group]),
            backgroundColor: 'rgba(249, 115, 22, 0.2)', // orange
            borderColor: 'rgb(249, 115, 22)',
            borderWidth: 2,
            pointBackgroundColor: 'rgb(249, 115, 22)',
            pointBorderColor: '#fff',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderColor: 'rgb(249, 115, 22)'
          }
        ]
      };

      setMuscleGroupChartData(data);
    }
  }, [exerciseMuscleMap, exercise_stats, loading, hasData]);

  // Prepare exercise progress data for visualization
  const exerciseProgressData = exercise_stats
    .filter(stat => stat.history_found && stat.progress)
    .map(stat => ({
      name: stat.name,
      weightChange: stat.progress.weight_change_pct,
      volumeChange: stat.progress.volume_change_pct,
      totalSets: stat.total_sets,
      maxWeight: stat.max_weight,
      maxVolume: stat.max_volume,
      plateau: stat.plateau_detected ? 'Yes' : 'No',
      // Add current workout data
      currentWeight: stat.most_recent ? stat.most_recent.weight : 0,
      currentReps: stat.most_recent ? stat.most_recent.reps : 0,
      avgWeight: stat.avg_weight || 0
    }));

  // Prepare workout type distribution data
  const workoutTypeLabels = [];
  const workoutTypeValues = [];
  if (workout_stats.exercise_type_distribution) {
    Object.entries(workout_stats.exercise_type_distribution).forEach(([type, count]) => {
      workoutTypeLabels.push(type.charAt(0).toUpperCase() + type.slice(1));
      workoutTypeValues.push(count);
    });
  }

  // Create chart data objects
  const exerciseProgressChartData = {
    labels: exerciseProgressData.map(item => item.name),
    datasets: [
      {
        label: 'Weight Progress %',
        data: exerciseProgressData.map(item => item.weightChange),
        backgroundColor: CHART_COLORS.primaryLight,
        borderColor: CHART_COLORS.primary,
        borderWidth: 1
      },
      {
        label: 'Volume Progress %',
        data: exerciseProgressData.map(item => item.volumeChange),
        backgroundColor: CHART_COLORS.secondaryLight,
        borderColor: CHART_COLORS.secondary,
        borderWidth: 1
      }
    ]
  };

  const workoutTypeChartData = {
    labels: workoutTypeLabels,
    datasets: [
      {
        data: workoutTypeValues,
        backgroundColor: PIE_COLORS.slice(0, workoutTypeLabels.length),
        borderColor: 'white',
        borderWidth: 1
      }
    ]
  };

  // Create weight comparison chart that shows current workout vs average
  const weightComparisonChartData = {
    labels: exerciseProgressData.map(item => item.name),
    datasets: [
      {
        label: 'Historical Average Weight',
        data: exerciseProgressData.map(item => item.avgWeight),
        backgroundColor: CHART_COLORS.neutralLight,
        borderColor: CHART_COLORS.neutral,
        borderWidth: 1
      },
      {
        label: 'Current Workout Weight',
        data: exerciseProgressData.map(item => item.currentWeight),
        backgroundColor: CHART_COLORS.highlightLight,
        borderColor: CHART_COLORS.highlight,
        borderWidth: 1
      }
    ]
  };

  // Chart options
  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 10,
          font: { size: 10 }
        }
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            return `${context.dataset.label}: ${context.raw.toFixed(1)}${context.dataset.label.includes('%') ? '%' : ' lbs'}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          font: { size: 10 }
        }
      },
      x: {
        ticks: {
          maxRotation: 45,
          minRotation: 45,
          font: { size: 10 }
        }
      }
    }
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          boxWidth: 10,
          font: { size: 10 }
        }
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = ((context.raw / total) * 100).toFixed(1);
            return `${context.label}: ${context.raw} (${percentage}%)`;
          }
        }
      }
    }
  };

  const radarChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 10,
          font: { size: 10 }
        }
      },
      title: {
        display: true,
        text: 'Muscle Groups Targeted in This Workout',
        font: { size: 12 }
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            return `${context.label}: ${context.raw} exercises`;
          }
        }
      }
    },
    scales: {
      r: {
        angleLines: {
          display: true
        },
        suggestedMin: 0,
        ticks: {
          font: { size: 9 },
          backdropPadding: 3
        },
        pointLabels: {
          font: { size: 10 }
        }
      }
    }
  };

  // Return early if no data is available
  if (!hasData) {
    return <div className="text-center p-4">No data available for visualization</div>;
  }

  return (
    <div className="workout-analysis-charts">
      <h3 className="text-xl font-bold mb-4">Workout Analysis Visualizations</h3>

      {/* Current Workout vs Average Comparison */}
      {exerciseProgressData.length > 0 && (
        <div className="mb-8">
          <h4 className="text-lg font-semibold mb-2">
            Current Workout vs Historical Average
            <span className="ml-2 text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded">CURRENT WORKOUT HIGHLIGHTED</span>
          </h4>
          <ResponsiveChartContainer>
            <Bar
              data={weightComparisonChartData}
              options={{
                ...barChartOptions,
                plugins: {
                  ...barChartOptions.plugins,
                  title: {
                    display: true,
                    text: 'Compare your current workout weights to your historical averages',
                    font: { size: 12 }
                  }
                }
              }}
            />
          </ResponsiveChartContainer>
        </div>
      )}

      {/* Exercise Progress Chart */}
      {exerciseProgressData.length > 0 && (
        <div className="mb-8">
          <h4 className="text-lg font-semibold mb-2">Long-Term Progress</h4>
          <ResponsiveChartContainer>
            <Bar
              data={exerciseProgressChartData}
              options={{
                ...barChartOptions,
                plugins: {
                  ...barChartOptions.plugins,
                  title: {
                    display: true,
                    text: 'Weight and volume progress since you started tracking',
                    font: { size: 12 }
                  }
                }
              }}
            />
          </ResponsiveChartContainer>
        </div>
      )}

      {/* Muscle Group Distribution for Current Workout */}
      {loading ? (
        <div className="mb-8">
          <h4 className="text-lg font-semibold mb-2">
            Muscle Group Balance
            <span className="ml-2 text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded">CURRENT WORKOUT</span>
          </h4>
          <ResponsiveChartContainer className="flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500 mb-2"></div>
              <p className="text-gray-500">Loading muscle group data...</p>
            </div>
          </ResponsiveChartContainer>
        </div>
      ) : muscleGroupChartData ? (
        <div className="mb-8">
          <h4 className="text-lg font-semibold mb-2">
            Muscle Group Balance
            <span className="ml-2 text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded">CURRENT WORKOUT</span>
          </h4>
          <ResponsiveChartContainer>
            <div className="w-full max-w-md h-full mx-auto">
              <Radar data={muscleGroupChartData} options={radarChartOptions} />
            </div>
          </ResponsiveChartContainer>
          <p className="mt-2 text-sm text-gray-500 text-center">
            This chart shows which muscle groups are targeted in your current workout
          </p>
        </div>
      ) : null}

      {/* Workout Type Distribution */}
      {workoutTypeLabels.length > 0 && (
        <div className="mb-8">
          <h4 className="text-lg font-semibold mb-2">Exercise Type Distribution</h4>
          <ResponsiveChartContainer>
            <Pie
              data={workoutTypeChartData}
              options={{
                ...pieChartOptions,
                plugins: {
                  ...pieChartOptions.plugins,
                  title: {
                    display: true,
                    text: 'Distribution of exercise types across your workout history',
                    font: { size: 12 }
                  }
                }
              }}
            />
          </ResponsiveChartContainer>
        </div>
      )}

      {/* Workout Frequency */}
      {workout_stats.workouts_per_week && (
        <div className="stats-summary p-4 bg-gray-100 rounded-lg mb-8">
          <h4 className="text-lg font-semibold mb-2">Workout Frequency</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <p className="text-sm text-gray-500">Weekly Average</p>
              <p className="text-2xl font-bold text-indigo-600">
                {workout_stats.workouts_per_week.toFixed(1)} <span className="text-sm font-normal text-gray-500">workouts/week</span>
              </p>
            </div>

            {workout_stats.avg_workouts_per_month && (
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <p className="text-sm text-gray-500">Monthly Average</p>
                <p className="text-2xl font-bold text-indigo-600">
                  {workout_stats.avg_workouts_per_month.toFixed(1)} <span className="text-sm font-normal text-gray-500">workouts/month</span>
                </p>
              </div>
            )}
          </div>

          {workout_stats.tracking_period_days && (
            <div className="mt-4 text-center">
              <p className="text-sm text-gray-500">
                You've been tracking workouts for <span className="font-semibold">{workout_stats.tracking_period_days}</span> days
              </p>
            </div>
          )}
        </div>
      )}

      {/* Plateaus and Achievements */}
      {exerciseProgressData.some(item => item.plateau === 'Yes') && (
        <div className="mb-8">
          <h4 className="text-lg font-semibold mb-2">Plateaus Detected</h4>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <ul className="list-disc pl-5 space-y-1">
              {exerciseProgressData
                .filter(item => item.plateau === 'Yes')
                .map(item => (
                  <li key={item.name} className="text-gray-700">
                    <span className="font-medium">{item.name}</span> - No improvement in recent sessions
                  </li>
                ))}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default WorkoutAnalysisCharts;
