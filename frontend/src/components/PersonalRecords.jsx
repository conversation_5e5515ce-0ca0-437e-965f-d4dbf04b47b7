// frontend/src/components/PersonalRecords.jsx
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { format } from 'date-fns';
import api from '../services/api';

const PersonalRecords = () => {
  const [records, setRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPersonalRecords = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await api.getPersonalRecords();
        setRecords(data);
      } catch (err) {
        console.error('Error fetching personal records:', err);
        setError('Failed to load personal records');
      } finally {
        setLoading(false);
      }
    };

    fetchPersonalRecords();
  }, []);

  // Format the value based on the exercise type
  const formatValue = (record) => {
    if (record.exercise_type === 'strength') {
      return `${record.value} ${record.unit}`;
    } else if (record.exercise_type === 'cardio_distance') {
      return `${record.value.toFixed(2)} ${record.unit}`;
    } else if (record.exercise_type === 'cardio_pace') {
      // Convert seconds to minutes:seconds format
      const minutes = Math.floor(record.value / 60);
      const seconds = Math.floor(record.value % 60);
      return `${minutes}:${seconds.toString().padStart(2, '0')} min/mile`;
    }
    return `${record.value} ${record.unit}`;
  };

  // Get a descriptive title for the record type
  const getRecordTitle = (record) => {
    if (record.exercise_type === 'strength') {
      return 'Max Weight';
    } else if (record.exercise_type === 'cardio_distance') {
      return 'Max Distance';
    } else if (record.exercise_type === 'cardio_pace') {
      return 'Best Pace';
    }
    return 'Record';
  };

  if (loading) {
    return (
      <div className="animate-pulse p-4">
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div className="h-24 bg-gray-100 rounded mb-2"></div>
        <div className="h-24 bg-gray-100 rounded mb-2"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-4 rounded-lg text-red-600">
        <p>{error}</p>
      </div>
    );
  }

  if (records.length === 0) {
    return (
      <div className="bg-gray-50 p-4 rounded-lg text-center">
        <p className="text-gray-600">No personal records yet. Complete more workouts to see your achievements!</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="bg-indigo-50 px-4 py-3 border-b border-indigo-100">
        <h2 className="text-lg font-semibold text-indigo-800">Personal Records</h2>
      </div>
      
      <div className="divide-y divide-gray-100">
        {records.map((record) => (
          <div key={`${record.exercise_name}-${record.exercise_type}`} className="p-4 hover:bg-gray-50">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-medium text-gray-900">{record.exercise_name}</h3>
                <p className="text-sm text-gray-500">{getRecordTitle(record)}</p>
              </div>
              <div className="text-right">
                <span className="text-lg font-bold text-indigo-600">{formatValue(record)}</span>
                <p className="text-xs text-gray-500">
                  {format(new Date(record.date), 'MMM d, yyyy')}
                </p>
              </div>
            </div>
            <div className="mt-2">
              <Link 
                to={`/workout/${record.workout_id}`}
                className="text-xs text-indigo-600 hover:text-indigo-800"
              >
                View Workout <i className="fas fa-chevron-right ml-1"></i>
              </Link>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PersonalRecords;
