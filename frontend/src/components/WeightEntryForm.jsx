// frontend/src/components/WeightEntryForm.jsx
import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import api from '../services/api';

const WeightEntryForm = ({ onWeightAdded, editEntry = null, onCancelEdit = null }) => {
  const [weight, setWeight] = useState('');
  const [bodyFat, setBodyFat] = useState('');
  const [date, setDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isEditing, setIsEditing] = useState(false);

  // If an entry is provided for editing, populate the form
  useEffect(() => {
    if (editEntry) {
      setWeight(editEntry.weight.toString());
      setBodyFat(editEntry.body_fat ? editEntry.body_fat.toString() : '');
      setDate(format(new Date(editEntry.date), 'yyyy-MM-dd'));
      setNotes(editEntry.notes || '');
      setIsEditing(true);
    } else {
      // Reset form when not editing
      setWeight('');
      setBodyFat('');
      setDate(format(new Date(), 'yyyy-MM-dd'));
      setNotes('');
      setIsEditing(false);
    }
  }, [editEntry]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Basic validation
    if (!weight || isNaN(parseFloat(weight)) || parseFloat(weight) <= 0) {
      setError('Please enter a valid weight');
      return;
    }

    // Validate body fat if provided
    if (bodyFat && (isNaN(parseFloat(bodyFat)) || parseFloat(bodyFat) < 0 || parseFloat(bodyFat) > 100)) {
      setError('Body fat percentage must be between 0 and 100');
      return;
    }

    if (!date) {
      setError('Please select a date');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const weightData = {
        weight: parseFloat(weight),
        body_fat: bodyFat ? parseFloat(bodyFat) : null,
        date: new Date(date),
        notes: notes || null
      };

      let response;

      if (isEditing && editEntry) {
        // Update existing entry
        response = await api.updateWeightEntry(editEntry.id, weightData);
      } else {
        // Add new entry
        response = await api.addWeightEntry(weightData);
      }

      // Reset form after successful submission
      if (!isEditing) {
        setWeight('');
        setBodyFat('');
        setNotes('');
      }

      // Notify parent component
      if (onWeightAdded) {
        onWeightAdded(response);
      }

      // If editing, cancel edit mode
      if (isEditing && onCancelEdit) {
        onCancelEdit();
      }

    } catch (err) {
      console.error('Error saving weight entry:', err);
      setError('Failed to save weight entry. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (onCancelEdit) {
      onCancelEdit();
    }
  };

  return (
    <div className="bg-white shadow-sm rounded-lg p-6">
      <h2 className="text-xl font-semibold mb-4">
        {isEditing ? 'Edit Weight Entry' : 'Add Weight Entry'}
      </h2>

      {error && (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded">
          <p>{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label htmlFor="weight" className="block text-sm font-medium text-gray-700 mb-1">
            Weight (lbs)
          </label>
          <input
            type="number"
            id="weight"
            value={weight}
            onChange={(e) => setWeight(e.target.value)}
            step="0.1"
            min="0"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="Enter your weight"
            required
          />
        </div>

        <div className="mb-4">
          <label htmlFor="bodyFat" className="block text-sm font-medium text-gray-700 mb-1">
            Body Fat % (optional)
          </label>
          <input
            type="number"
            id="bodyFat"
            value={bodyFat}
            onChange={(e) => setBodyFat(e.target.value)}
            step="0.1"
            min="0"
            max="100"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="Enter your body fat percentage"
          />
        </div>

        <div className="mb-4">
          <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
            Date
          </label>
          <input
            type="date"
            id="date"
            value={date}
            onChange={(e) => setDate(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            required
          />
        </div>

        <div className="mb-4">
          <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
            Notes (optional)
          </label>
          <textarea
            id="notes"
            value={notes || ''}
            onChange={(e) => setNotes(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            rows="2"
            placeholder="Add any notes about this weight entry"
          />
        </div>

        <div className="flex justify-end space-x-2">
          {isEditing && (
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              disabled={loading}
            >
              Cancel
            </button>
          )}

          <button
            type="submit"
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            disabled={loading}
          >
            {loading ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </span>
            ) : isEditing ? 'Update' : 'Save'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default WeightEntryForm;
