// frontend/src/components/StrengthProgressChart.jsx
import React, { useState, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import { format } from 'date-fns';
import ResponsiveChartContainer from './ResponsiveChartContainer';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const StrengthProgressChart = ({ workouts }) => {
  const [chartData, setChartData] = useState(null);
  const [selectedExercise, setSelectedExercise] = useState('');
  const [availableExercises, setAvailableExercises] = useState([]);

  // Process workouts to extract strength exercises
  useEffect(() => {
    if (!workouts || workouts.length === 0) return;

    // Extract all strength exercises with their names
    const strengthExercises = new Set();
    workouts.forEach(workout => {
      workout.exercises.forEach(exercise => {
        if (exercise.exercise_type === 'strength' && exercise.sets && exercise.sets.length > 0) {
          strengthExercises.add(exercise.name);
        }
      });
    });

    // Convert to array and sort alphabetically
    const exercisesList = Array.from(strengthExercises).sort();
    setAvailableExercises(exercisesList);

    // Set default selected exercise if available
    if (exercisesList.length > 0 && !selectedExercise) {
      setSelectedExercise(exercisesList[0]);
    }
  }, [workouts, selectedExercise]);

  // Generate chart data when selected exercise changes
  useEffect(() => {
    if (!selectedExercise || !workouts || workouts.length === 0) return;

    // Find all instances of the selected exercise across workouts
    const exerciseData = [];
    workouts.forEach(workout => {
      workout.exercises.forEach(exercise => {
        if (exercise.name === selectedExercise && exercise.exercise_type === 'strength') {
          // Find the max weight for this exercise in this workout
          let maxWeight = 0;
          exercise.sets.forEach(set => {
            if (set.weight && set.weight > maxWeight) {
              maxWeight = set.weight;
            }
          });

          if (maxWeight > 0) {
            exerciseData.push({
              date: new Date(workout.date),
              maxWeight
            });
          }
        }
      });
    });

    // Sort by date
    exerciseData.sort((a, b) => a.date - b.date);

    if (exerciseData.length > 0) {
      // Prepare data for Chart.js
      const data = {
        labels: exerciseData.map(item => format(item.date, 'MMM d, yyyy')),
        datasets: [
          {
            label: 'Max Weight (lbs)',
            data: exerciseData.map(item => item.maxWeight),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.5)',
            tension: 0.1
          }
        ]
      };

      setChartData(data);
    } else {
      setChartData(null);
    }
  }, [selectedExercise, workouts]);

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          // Ensure labels are readable on mobile
          boxWidth: 10,
          font: {
            size: 10
          }
        }
      },
      title: {
        display: true,
        text: `Strength Progress: ${selectedExercise}`,
        font: {
          size: 14
        }
      },
      tooltip: {
        callbacks: {
          title: (tooltipItems) => {
            return tooltipItems[0].label;
          },
          label: (context) => {
            return `Max Weight: ${context.parsed.y} lbs`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: false,
        title: {
          display: true,
          text: 'Weight (lbs)',
          font: {
            size: 10
          }
        },
        ticks: {
          font: {
            size: 9
          }
        }
      },
      x: {
        title: {
          display: true,
          text: 'Workout Date',
          font: {
            size: 10
          }
        },
        ticks: {
          maxRotation: 90,
          minRotation: 45,
          font: {
            size: 8
          }
        }
      }
    }
  };

  return (
    <div className="bg-white shadow-sm rounded-lg p-6">
      <h2 className="text-xl font-semibold mb-4">Strength Progress Tracker</h2>

      {availableExercises.length > 0 ? (
        <div>
          <div className="mb-4">
            <label htmlFor="exercise-select" className="block text-sm font-medium text-gray-700 mb-1">
              Select Exercise:
            </label>
            <select
              id="exercise-select"
              value={selectedExercise}
              onChange={(e) => setSelectedExercise(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
              {availableExercises.map(exercise => (
                <option key={exercise} value={exercise}>
                  {exercise}
                </option>
              ))}
            </select>
          </div>

          {chartData ? (
            <ResponsiveChartContainer>
              <Line data={chartData} options={chartOptions} />
            </ResponsiveChartContainer>
          ) : (
            <ResponsiveChartContainer className="flex items-center justify-center bg-gray-50 rounded-lg">
              <p className="text-gray-500">No data available for this exercise</p>
            </ResponsiveChartContainer>
          )}
        </div>
      ) : (
        <ResponsiveChartContainer className="flex items-center justify-center bg-gray-50 rounded-lg">
          <p className="text-gray-500">No strength exercises found in your workout history</p>
        </ResponsiveChartContainer>
      )}
    </div>
  );
};

export default StrengthProgressChart;
