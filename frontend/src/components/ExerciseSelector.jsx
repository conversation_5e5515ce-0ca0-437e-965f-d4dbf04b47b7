// frontend/src/components/ExerciseSelector.jsx
import React, { useState, useEffect, useRef } from 'react';
import api from '../services/api';

const ExerciseSelector = ({ onSelectExercise, onClose }) => {
  const [exercises, setExercises] = useState([]);
  const [muscleGroups, setMuscleGroups] = useState([]);
  const [equipment, setEquipment] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [selectedMuscleGroup, setSelectedMuscleGroup] = useState('');
  const [selectedEquipment, setSelectedEquipment] = useState('');
  
  const searchInputRef = useRef(null);
  
  // Load initial data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError('');
        
        // Fetch exercises, muscle groups, and equipment in parallel
        const [exercisesData, muscleGroupsData, equipmentData] = await Promise.all([
          api.getExerciseLibrary(),
          api.getMuscleGroups(),
          api.getEquipment()
        ]);
        
        setExercises(exercisesData);
        setMuscleGroups(muscleGroupsData);
        setEquipment(equipmentData);
      } catch (err) {
        console.error('Error fetching exercise data:', err);
        setError('Failed to load exercise data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
    
    // Focus the search input when the component mounts
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);
  
  // Filter exercises when filters change
  useEffect(() => {
    const fetchFilteredExercises = async () => {
      try {
        setLoading(true);
        
        const params = {
          search: searchTerm,
          exercise_type: selectedType,
          muscle_group: selectedMuscleGroup,
          equipment: selectedEquipment
        };
        
        const filteredExercises = await api.getExerciseLibrary(params);
        setExercises(filteredExercises);
      } catch (err) {
        console.error('Error filtering exercises:', err);
        setError('Failed to filter exercises. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    // Debounce the filter request
    const timeoutId = setTimeout(() => {
      fetchFilteredExercises();
    }, 300);
    
    return () => clearTimeout(timeoutId);
  }, [searchTerm, selectedType, selectedMuscleGroup, selectedEquipment]);
  
  const handleSelectExercise = (exercise) => {
    onSelectExercise({
      name: exercise.name,
      exercise_type: exercise.exercise_type,
      description: exercise.description
    });
    onClose();
  };
  
  const handleClearFilters = () => {
    setSearchTerm('');
    setSelectedType('');
    setSelectedMuscleGroup('');
    setSelectedEquipment('');
    
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        <div className="p-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-dark-800">Select Exercise</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <i className="fas fa-times"></i>
          </button>
        </div>
        
        {/* Search and Filters */}
        <div className="p-4 border-b border-gray-200">
          <div className="relative mb-4">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i className="fas fa-search text-gray-400"></i>
            </div>
            <input
              ref={searchInputRef}
              type="text"
              className="form-input pl-10 w-full"
              placeholder="Search exercises..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Exercise Type Filter */}
            <div>
              <label className="block text-sm font-medium text-dark-700 mb-1">
                Exercise Type
              </label>
              <select
                className="form-select w-full"
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
              >
                <option value="">All Types</option>
                <option value="strength">Strength</option>
                <option value="cardio">Cardio</option>
                <option value="flexibility">Flexibility</option>
                <option value="other">Other</option>
              </select>
            </div>
            
            {/* Muscle Group Filter */}
            <div>
              <label className="block text-sm font-medium text-dark-700 mb-1">
                Muscle Group
              </label>
              <select
                className="form-select w-full"
                value={selectedMuscleGroup}
                onChange={(e) => setSelectedMuscleGroup(e.target.value)}
              >
                <option value="">All Muscle Groups</option>
                {muscleGroups.map((group) => (
                  <option key={group.id} value={group.name}>
                    {group.name.charAt(0).toUpperCase() + group.name.slice(1)}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Equipment Filter */}
            <div>
              <label className="block text-sm font-medium text-dark-700 mb-1">
                Equipment
              </label>
              <select
                className="form-select w-full"
                value={selectedEquipment}
                onChange={(e) => setSelectedEquipment(e.target.value)}
              >
                <option value="">All Equipment</option>
                {equipment.map((item) => (
                  <option key={item.id} value={item.name}>
                    {item.name.charAt(0).toUpperCase() + item.name.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="mt-3 flex justify-end">
            <button
              onClick={handleClearFilters}
              className="text-primary-600 hover:text-primary-800 text-sm font-medium"
            >
              Clear Filters
            </button>
          </div>
        </div>
        
        {/* Exercise List */}
        <div className="flex-1 overflow-y-auto p-4">
          {loading ? (
            <div className="flex justify-center items-center h-40">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600"></div>
            </div>
          ) : error ? (
            <div className="text-red-500 text-center p-4">{error}</div>
          ) : exercises.length === 0 ? (
            <div className="text-center p-8 text-gray-500">
              <i className="fas fa-search text-4xl mb-2"></i>
              <p>No exercises found. Try adjusting your filters.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {exercises.map((exercise) => (
                <div
                  key={exercise.id}
                  className="card p-4 cursor-pointer hover:border-primary-300 transition-colors"
                  onClick={() => handleSelectExercise(exercise)}
                >
                  <h3 className="font-medium text-dark-800 mb-1">{exercise.name}</h3>
                  <div className="flex items-center text-xs text-gray-500 mb-2">
                    <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded-full capitalize mr-2">
                      {exercise.exercise_type}
                    </span>
                    {exercise.difficulty && (
                      <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full capitalize">
                        {exercise.difficulty}
                      </span>
                    )}
                  </div>
                  {exercise.description && (
                    <p className="text-sm text-gray-600 line-clamp-2">{exercise.description}</p>
                  )}
                  <div className="mt-2 flex flex-wrap gap-1">
                    {exercise.muscle_groups.map((group) => (
                      <span key={group.id} className="text-xs bg-secondary-100 text-secondary-800 px-2 py-0.5 rounded-full capitalize">
                        {group.name}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        
        <div className="p-4 border-t border-gray-200 flex justify-between">
          <button
            onClick={onClose}
            className="btn btn-outline"
          >
            Cancel
          </button>
          <div className="text-sm text-gray-500">
            {exercises.length} exercises found
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExerciseSelector;
