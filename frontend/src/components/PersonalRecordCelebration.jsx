// frontend/src/components/PersonalRecordCelebration.jsx
import React, { useState, useEffect } from 'react';
import Confetti from 'react-confetti';
import { useWindowSize } from 'react-use';

const PersonalRecordCelebration = ({ show, record, onClose }) => {
  const { width, height } = useWindowSize();
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    if (show) {
      setIsVisible(true);
      // Auto-hide after 5 seconds
      const timer = setTimeout(() => {
        setIsVisible(false);
        if (onClose) onClose();
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, [show, onClose]);
  
  if (!isVisible || !record) return null;
  
  // Format the value based on the exercise type
  const formatValue = (record) => {
    if (record.exercise_type === 'strength') {
      return `${record.value} ${record.unit}`;
    } else if (record.exercise_type === 'cardio_distance') {
      return `${record.value.toFixed(2)} ${record.unit}`;
    } else if (record.exercise_type === 'cardio_pace') {
      // Convert seconds to minutes:seconds format
      const minutes = Math.floor(record.value / 60);
      const seconds = Math.floor(record.value % 60);
      return `${minutes}:${seconds.toString().padStart(2, '0')} min/mile`;
    }
    return `${record.value} ${record.unit}`;
  };
  
  // Get a descriptive title for the record type
  const getRecordTitle = (record) => {
    if (record.exercise_type === 'strength') {
      return 'Max Weight';
    } else if (record.exercise_type === 'cardio_distance') {
      return 'Max Distance';
    } else if (record.exercise_type === 'cardio_pace') {
      return 'Best Pace';
    }
    return 'Record';
  };
  
  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      {/* Confetti overlay */}
      <Confetti
        width={width}
        height={height}
        recycle={false}
        numberOfPieces={500}
        gravity={0.2}
      />
      
      {/* Celebration modal */}
      <div className="bg-white rounded-lg shadow-xl p-6 max-w-md mx-4 relative z-10 border-4 border-indigo-500">
        <button
          onClick={() => {
            setIsVisible(false);
            if (onClose) onClose();
          }}
          className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
        >
          <i className="fas fa-times"></i>
        </button>
        
        <div className="text-center">
          <div className="text-5xl mb-4">🏆</div>
          <h2 className="text-2xl font-bold text-indigo-700 mb-2">New Personal Record!</h2>
          <p className="text-lg font-medium text-gray-800 mb-1">{record.exercise_name}</p>
          <p className="text-md text-gray-600 mb-3">{getRecordTitle(record)}</p>
          
          <div className="bg-indigo-100 rounded-lg p-4 mb-4">
            <span className="text-3xl font-bold text-indigo-800">{formatValue(record)}</span>
          </div>
          
          <p className="text-gray-600">Keep up the great work! 💪</p>
        </div>
      </div>
    </div>
  );
};

export default PersonalRecordCelebration;
