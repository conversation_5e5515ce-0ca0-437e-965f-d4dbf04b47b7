// frontend/src/components/WorkoutAnalysis.jsx
import React, { useState, useEffect } from 'react';
import api from '../services/api';
import WorkoutAnalysisCharts from './WorkoutAnalysisCharts';

const WorkoutAnalysis = ({ workoutId, autoFetch = false }) => {
  const [analysis, setAnalysis] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const [expanded, setExpanded] = useState({
    summary: true,
    progress: false,
    insights: false,
    recommendations: false,
    charts: false
  });

  // Debug utility
  const debug = (message, ...args) => {
    console.log(`[WorkoutAnalysis] ${message}`, ...args);
  };

  const fetchAnalysis = async () => {
    if (!workoutId) {
      debug('No workout ID provided');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      debug(`Attempting to fetch analysis for workout ${workoutId}`);

      const data = await api.getWorkoutAnalysis(workoutId);
      debug('Analysis data successfully retrieved:', data);

      if (!data) {
        throw new Error('Received empty analysis data');
      }

      setAnalysis(data);
    } catch (err) {
      debug('Error fetching workout analysis:', err);
      setError('Could not load workout analysis. The analysis service might be unavailable or missing configuration.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch analysis when component mounts or when workoutId/retryCount changes
  useEffect(() => {
    // Always fetch analysis when the component is rendered
    fetchAnalysis();
  }, [workoutId, retryCount]);

  const handleRetry = () => {
    debug(`Retrying analysis fetch, attempt #${retryCount + 1}`);
    fetchAnalysis();
  };

  const toggleSection = (section) => {
    setExpanded({
      ...expanded,
      [section]: !expanded[section]
    });
  };

  if (loading) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <div className="flex items-center space-x-4 mb-4">
          <div className="h-5 w-5 bg-indigo-600 rounded-full animate-pulse"></div>
          <h3 className="text-lg font-semibold text-gray-800">Analyzing your workout...</h3>
        </div>
        <div className="space-y-3">
          <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded animate-pulse w-full"></div>
          <div className="h-4 bg-gray-200 rounded animate-pulse w-5/6"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white border border-gray-200 p-6 rounded-lg shadow-sm">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">Workout Analysis</h3>
        <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg mb-4">
          <p className="text-yellow-700">{error}</p>
        </div>
        <button
          onClick={handleRetry}
          className="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded"
        >
          Retry Analysis
        </button>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="bg-white border border-gray-200 p-6 rounded-lg shadow-sm">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Workout Analysis</h3>
        <div className="flex flex-col items-center justify-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mb-4"></div>
          <p className="text-gray-600">Generating workout analysis...</p>
          <p className="text-gray-500 text-sm mt-2">This may take a few moments</p>
        </div>
      </div>
    );
  }

  if (!analysis) {
    return null;
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
      <div className="bg-indigo-50 px-6 py-4 border-b border-indigo-100">
        <h3 className="text-xl font-semibold text-indigo-800">Workout Analysis</h3>
        <p className="text-indigo-600 text-sm">
          AI-powered insights about your workout performance
        </p>
      </div>

      <div className="p-6">
        {/* Summary Section */}
        <div className="mb-4">
          <div
            className="flex justify-between items-center cursor-pointer"
            onClick={() => toggleSection('summary')}
          >
            <h4 className="font-semibold text-lg text-gray-800">Summary</h4>
            <span className="text-indigo-600">
              {expanded.summary ? '▼' : '▶'}
            </span>
          </div>

          {expanded.summary && analysis.summary && (
            <div className="mt-2 text-gray-700 bg-gray-50 p-4 rounded-lg">
              {analysis.summary.split('\n').map((paragraph, i) => (
                <p key={i} className="mb-2">{paragraph}</p>
              ))}
            </div>
          )}
        </div>

        {/* Progress Section */}
        <div className="mb-4">
          <div
            className="flex justify-between items-center cursor-pointer"
            onClick={() => toggleSection('progress')}
          >
            <h4 className="font-semibold text-lg text-gray-800">Progress</h4>
            <span className="text-indigo-600">
              {expanded.progress ? '▼' : '▶'}
            </span>
          </div>

          {expanded.progress && analysis.progress && (
            <div className="mt-2 text-gray-700 bg-gray-50 p-4 rounded-lg">
              {analysis.progress.split('\n').map((paragraph, i) => (
                <p key={i} className="mb-2">{paragraph}</p>
              ))}
            </div>
          )}
        </div>

        {/* Insights Section */}
        <div className="mb-4">
          <div
            className="flex justify-between items-center cursor-pointer"
            onClick={() => toggleSection('insights')}
          >
            <h4 className="font-semibold text-lg text-gray-800">Insights</h4>
            <span className="text-indigo-600">
              {expanded.insights ? '▼' : '▶'}
            </span>
          </div>

          {expanded.insights && analysis.insights && (
            <div className="mt-2 text-gray-700 bg-gray-50 p-4 rounded-lg">
              {analysis.insights.split('\n').map((paragraph, i) => (
                <p key={i} className="mb-2">{paragraph}</p>
              ))}
            </div>
          )}
        </div>

        {/* Recommendations Section */}
        <div className="mb-4">
          <div
            className="flex justify-between items-center cursor-pointer"
            onClick={() => toggleSection('recommendations')}
          >
            <h4 className="font-semibold text-lg text-gray-800">Recommendations</h4>
            <span className="text-indigo-600">
              {expanded.recommendations ? '▼' : '▶'}
            </span>
          </div>

          {expanded.recommendations && analysis.recommendations && (
            <div className="mt-2 text-gray-700 bg-gray-50 p-4 rounded-lg">
              {analysis.recommendations.split('\n').map((paragraph, i) => (
                <p key={i} className="mb-2">{paragraph}</p>
              ))}
            </div>
          )}
        </div>

        {/* Charts Section */}
        {analysis.processed_data && (
          <div className="mb-4">
            <div
              className="flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection('charts')}
            >
              <h4 className="font-semibold text-lg text-gray-800">
                Data Visualizations
                <span className="ml-2 text-xs text-indigo-600 bg-indigo-100 px-2 py-1 rounded">NEW</span>
              </h4>
              <span className="text-indigo-600">
                {expanded.charts ? '▼' : '▶'}
              </span>
            </div>

            {expanded.charts && (
              <div className="mt-4">
                <WorkoutAnalysisCharts processedData={analysis.processed_data} />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkoutAnalysis;