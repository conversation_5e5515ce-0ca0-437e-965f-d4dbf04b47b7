// frontend/src/components/WorkoutFilter.jsx
import React, { useState } from 'react';

const WorkoutFilter = ({ onFilterChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filters, setFilters] = useState({
    exerciseType: '',
    dateRange: '',
    searchTerm: ''
  });

  const handleFilterChange = (field, value) => {
    const newFilters = { ...filters, [field]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const clearFilters = () => {
    const resetFilters = {
      exerciseType: '',
      dateRange: '',
      searchTerm: ''
    };
    setFilters(resetFilters);
    onFilterChange(resetFilters);
  };

  return (
    <div className="mb-4">
      <div className="flex justify-between items-center mb-2">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="text-primary-600 hover:text-primary-800 text-sm font-medium flex items-center"
        >
          <i className="fas fa-filter mr-1"></i>
          {isOpen ? 'Hide Filters' : 'Show Filters'}
        </button>
        
        {Object.values(filters).some(v => v) && (
          <button
            onClick={clearFilters}
            className="text-gray-500 hover:text-gray-700 text-xs"
          >
            Clear Filters
          </button>
        )}
      </div>
      
      {isOpen && (
        <div className="bg-white p-4 rounded-lg shadow-sm mb-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Exercise Type Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Exercise Type
            </label>
            <select
              value={filters.exerciseType}
              onChange={(e) => handleFilterChange('exerciseType', e.target.value)}
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
            >
              <option value="">All Types</option>
              <option value="strength">Strength</option>
              <option value="cardio">Cardio</option>
              <option value="flexibility">Flexibility</option>
            </select>
          </div>
          
          {/* Date Range Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Date Range
            </label>
            <select
              value={filters.dateRange}
              onChange={(e) => handleFilterChange('dateRange', e.target.value)}
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
            >
              <option value="">All Time</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="3months">Last 3 Months</option>
              <option value="year">This Year</option>
            </select>
          </div>
          
          {/* Search Term */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <input
              type="text"
              value={filters.searchTerm}
              onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
              placeholder="Search exercises..."
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default WorkoutFilter;
