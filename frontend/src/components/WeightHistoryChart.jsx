// frontend/src/components/WeightHistoryChart.jsx
import React, { useMemo, useState } from 'react';
import { Line } from 'react-chartjs-2';
import { format, subDays, subWeeks, subMonths, subYears } from 'date-fns';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale
} from 'chart.js';
import 'chartjs-adapter-date-fns';
import ResponsiveChartContainer from './ResponsiveChartContainer';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale
);

// Helper function to get a human-readable label for timeframe
const getTimeframeLabel = (timeframe) => {
  switch (timeframe) {
    case '7days':
      return 'Last 7 Days';
    case '30days':
      return 'Last 30 Days';
    case '90days':
      return 'Last 90 Days';
    case '6months':
      return 'Last 6 Months';
    case '1year':
      return 'Last Year';
    default:
      return 'All Time';
  }
};

const WeightHistoryChart = ({ weightEntries }) => {
  const [timeframe, setTimeframe] = useState('all'); // Default to showing all data
  const [displayMetrics, setDisplayMetrics] = useState('both'); // Default to showing both weight and body fat

  // Filter entries based on selected timeframe
  const filteredEntries = useMemo(() => {
    if (timeframe === 'all' || weightEntries.length === 0) {
      console.log('Using all weight entries:', weightEntries.length);
      return weightEntries;
    }

    const now = new Date();
    let cutoffDate;

    switch (timeframe) {
      case '7days':
        cutoffDate = subDays(now, 7);
        break;
      case '30days':
        cutoffDate = subDays(now, 30);
        break;
      case '90days':
        cutoffDate = subDays(now, 90);
        break;
      case '6months':
        cutoffDate = subMonths(now, 6);
        break;
      case '1year':
        cutoffDate = subYears(now, 1);
        break;
      default:
        return weightEntries;
    }

    console.log('Filtering entries by date, cutoff:', cutoffDate);
    const filtered = weightEntries.filter(entry => {
      // Parse the date string to ensure proper comparison
      const entryDate = new Date(entry.date);

      // Ensure both dates are compared as UTC dates to avoid timezone issues
      const entryTime = entryDate.getTime();
      const cutoffTime = cutoffDate.getTime();

      const isIncluded = entryTime >= cutoffTime;
      console.log('Entry date:', entryDate, 'Entry time:', entryTime, 'Cutoff time:', cutoffTime, 'included:', isIncluded);
      return isIncluded;
    });

    console.log('Filtered entries:', filtered.length, 'out of', weightEntries.length);
    return filtered;
  }, [weightEntries, timeframe]);

  // Sort entries by date (oldest to newest) for proper chart display
  const sortedEntries = useMemo(() => {
    return [...filteredEntries].sort((a, b) => new Date(a.date) - new Date(b.date));
  }, [filteredEntries]);

  // Prepare data for the chart
  const chartData = useMemo(() => {
    console.log('Rebuilding chart data with', sortedEntries.length, 'entries for timeframe:', timeframe);

    // Create datasets based on selected metrics
    const datasets = [];

    // Add weight dataset if selected
    if (displayMetrics === 'weight' || displayMetrics === 'both') {
      datasets.push({
        label: 'Weight (lbs)',
        data: sortedEntries.map(entry => ({
          x: new Date(entry.date),
          y: entry.weight
        })),
        borderColor: 'rgb(79, 70, 229)', // Indigo-600
        backgroundColor: 'rgba(79, 70, 229, 0.1)',
        borderWidth: 2,
        pointBackgroundColor: 'rgb(79, 70, 229)',
        pointRadius: 4,
        pointHoverRadius: 6,
        fill: true,
        tension: 0.2, // Slight curve for the line
        yAxisID: 'y'
      });
    }

    // Add body fat dataset if selected and there are entries with body fat data
    if ((displayMetrics === 'bodyFat' || displayMetrics === 'both') &&
        sortedEntries.some(entry => entry.body_fat !== null && entry.body_fat !== undefined)) {
      datasets.push({
        label: 'Body Fat (%)',
        data: sortedEntries
          .filter(entry => entry.body_fat !== null && entry.body_fat !== undefined)
          .map(entry => ({
            x: new Date(entry.date),
            y: entry.body_fat
          })),
        borderColor: 'rgb(220, 38, 38)', // Red-600
        backgroundColor: 'rgba(220, 38, 38, 0.1)',
        borderWidth: 2,
        pointBackgroundColor: 'rgb(220, 38, 38)',
        pointRadius: 4,
        pointHoverRadius: 6,
        fill: true,
        tension: 0.2,
        yAxisID: 'y1'
      });
    }

    return { datasets };
  }, [sortedEntries, timeframe, displayMetrics]);

  // Chart options
  const chartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: false,
        position: 'left',
        title: {
          display: true,
          text: 'Weight (lbs)',
          font: {
            size: 10
          }
        },
        ticks: {
          font: {
            size: 9
          }
        },
        display: displayMetrics === 'weight' || displayMetrics === 'both'
      },
      y1: {
        beginAtZero: false,
        position: 'right',
        title: {
          display: true,
          text: 'Body Fat (%)',
          font: {
            size: 10
          },
          color: 'rgb(220, 38, 38)' // Red-600
        },
        ticks: {
          font: {
            size: 9
          },
          color: 'rgb(220, 38, 38)' // Red-600
        },
        grid: {
          drawOnChartArea: false // Only draw grid lines for the primary y-axis
        },
        min: 0,
        max: 100,
        display: displayMetrics === 'bodyFat' || displayMetrics === 'both'
      },
      x: {
        type: 'time',
        time: {
          unit: 'day',
          tooltipFormat: 'PPP', // Format for tooltip (e.g., "Jan 1, 2023")
          displayFormats: {
            day: 'MMM d' // Format for axis labels (e.g., "Jan 1")
          }
        },
        title: {
          display: true,
          text: 'Date',
          font: {
            size: 10
          }
        },
        ticks: {
          maxRotation: 90,
          minRotation: 45,
          font: {
            size: 8
          }
        }
      }
    },
    plugins: {
      legend: {
        display: true,
        position: 'top',
        labels: {
          boxWidth: 10,
          font: {
            size: 10
          }
        }
      },
      title: {
        display: true,
        text: `Weight History ${timeframe !== 'all' ? '- ' + getTimeframeLabel(timeframe) : ''}`,
        font: {
          size: 14
        }
      },
      tooltip: {
        callbacks: {
          title: (tooltipItems) => {
            return format(new Date(tooltipItems[0].parsed.x), 'MMMM d, yyyy');
          },
          label: (tooltipItem) => {
            if (tooltipItem.dataset.label === 'Weight (lbs)') {
              return `Weight: ${tooltipItem.parsed.y} lbs`;
            } else if (tooltipItem.dataset.label === 'Body Fat (%)') {
              return `Body Fat: ${tooltipItem.parsed.y}%`;
            }
            return tooltipItem.dataset.label + ': ' + tooltipItem.parsed.y;
          },
          afterLabel: (tooltipItem) => {
            // Find the entry that matches this date point
            const entry = sortedEntries.find(entry =>
              new Date(entry.date).getTime() === new Date(tooltipItem.parsed.x).getTime()
            );
            return entry?.notes ? `Notes: ${entry.notes}` : '';
          }
        }
      }
    }
  }), [timeframe, sortedEntries]);

  // Calculate statistics based on filtered entries
  const calculateStats = () => {
    if (sortedEntries.length === 0) return null;

    // Weight statistics
    const weights = sortedEntries.map(entry => entry.weight);
    const currentWeight = weights[weights.length - 1];
    const initialWeight = weights[0];
    const weightChange = currentWeight - initialWeight;
    const weightPercentChange = (weightChange / initialWeight) * 100;

    const maxWeight = Math.max(...weights);
    const minWeight = Math.min(...weights);

    // Body fat statistics
    const entriesWithBodyFat = sortedEntries.filter(entry =>
      entry.body_fat !== null && entry.body_fat !== undefined
    );

    let bodyFatStats = null;
    if (entriesWithBodyFat.length > 0) {
      const bodyFats = entriesWithBodyFat.map(entry => entry.body_fat);
      const currentBodyFat = bodyFats[bodyFats.length - 1];
      const initialBodyFat = bodyFats[0];
      const bodyFatChange = currentBodyFat - initialBodyFat;
      const bodyFatPercentChange = (bodyFatChange / initialBodyFat) * 100;

      const maxBodyFat = Math.max(...bodyFats);
      const minBodyFat = Math.min(...bodyFats);

      bodyFatStats = {
        currentBodyFat,
        initialBodyFat,
        bodyFatChange,
        bodyFatPercentChange,
        maxBodyFat,
        minBodyFat,
        bodyFatEntryCount: entriesWithBodyFat.length
      };
    }

    // Add entry count for the selected timeframe
    const entryCount = sortedEntries.length;

    return {
      // Weight stats
      currentWeight,
      initialWeight,
      weightChange,
      weightPercentChange,
      maxWeight,
      minWeight,
      // Body fat stats
      bodyFatStats,
      // General stats
      entryCount,
      hasBodyFatData: entriesWithBodyFat.length > 0
    };
  };

  const stats = calculateStats();

  return (
    <div className="bg-white shadow-sm rounded-lg p-6">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-4 space-y-2 md:space-y-0">
        <h2 className="text-xl font-semibold">Weight History</h2>

        <div className="flex flex-wrap items-center space-x-4">
          <div className="flex items-center">
            <label htmlFor="metrics-select" className="mr-2 text-sm text-gray-600">
              Display:
            </label>
            <select
              id="metrics-select"
              value={displayMetrics}
              onChange={(e) => setDisplayMetrics(e.target.value)}
              className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
            >
              <option value="both">Weight & Body Fat</option>
              <option value="weight">Weight Only</option>
              <option value="bodyFat">Body Fat Only</option>
            </select>
          </div>

          <div className="flex items-center">
            <label htmlFor="timeframe-select" className="mr-2 text-sm text-gray-600">
              Timeframe:
            </label>
            <select
              id="timeframe-select"
              value={timeframe}
              onChange={(e) => setTimeframe(e.target.value)}
              className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
            >
              <option value="all">All Time</option>
              <option value="7days">Last 7 Days</option>
              <option value="30days">Last 30 Days</option>
              <option value="90days">Last 90 Days</option>
              <option value="6months">Last 6 Months</option>
              <option value="1year">Last Year</option>
            </select>
          </div>
        </div>
      </div>

      {weightEntries.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>No weight entries yet. Add your first weight entry to see your progress over time.</p>
        </div>
      ) : (
        <>
          {/* Stats summary */}
          {stats && (
            <>
              <div className="mb-2">
                <p className="text-sm text-gray-500">
                  {timeframe !== 'all' ? `Statistics for ${getTimeframeLabel(timeframe)}` : 'All-Time Statistics'}
                  <span className="ml-1">({stats.entryCount} entries)</span>
                </p>
              </div>

              {/* Weight Stats */}
              {(displayMetrics === 'weight' || displayMetrics === 'both') && (
                <>
                  <h3 className="text-md font-semibold text-indigo-600 mb-2">Weight Statistics</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                    <div className="bg-gray-50 p-3 rounded">
                      <p className="text-sm text-gray-500">Current</p>
                      <p className="text-xl font-bold">{stats.currentWeight} lbs</p>
                    </div>

                    <div className="bg-gray-50 p-3 rounded">
                      <p className="text-sm text-gray-500">Change</p>
                      <p className={`text-xl font-bold ${stats.weightChange < 0 ? 'text-green-600' : stats.weightChange > 0 ? 'text-red-600' : 'text-gray-600'}`}>
                        {stats.weightChange > 0 ? '+' : ''}{stats.weightChange.toFixed(1)} lbs
                        <span className="text-sm font-normal ml-1">
                          ({stats.weightPercentChange > 0 ? '+' : ''}{stats.weightPercentChange.toFixed(1)}%)
                        </span>
                      </p>
                    </div>

                    <div className="bg-gray-50 p-3 rounded">
                      <p className="text-sm text-gray-500">Range</p>
                      <p className="text-xl font-bold">
                        {stats.minWeight} - {stats.maxWeight} lbs
                      </p>
                    </div>
                  </div>
                </>
              )}

              {/* Body Fat Stats */}
              {(displayMetrics === 'bodyFat' || displayMetrics === 'both') && stats.hasBodyFatData && stats.bodyFatStats && (
                <>
                  <h3 className="text-md font-semibold text-red-600 mb-2">Body Fat Statistics</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                    <div className="bg-gray-50 p-3 rounded">
                      <p className="text-sm text-gray-500">Current</p>
                      <p className="text-xl font-bold">{stats.bodyFatStats.currentBodyFat}%</p>
                    </div>

                    <div className="bg-gray-50 p-3 rounded">
                      <p className="text-sm text-gray-500">Change</p>
                      <p className={`text-xl font-bold ${stats.bodyFatStats.bodyFatChange < 0 ? 'text-green-600' : stats.bodyFatStats.bodyFatChange > 0 ? 'text-red-600' : 'text-gray-600'}`}>
                        {stats.bodyFatStats.bodyFatChange > 0 ? '+' : ''}{stats.bodyFatStats.bodyFatChange.toFixed(1)}%
                        <span className="text-sm font-normal ml-1">
                          ({stats.bodyFatStats.bodyFatPercentChange > 0 ? '+' : ''}{stats.bodyFatStats.bodyFatPercentChange.toFixed(1)}%)
                        </span>
                      </p>
                    </div>

                    <div className="bg-gray-50 p-3 rounded">
                      <p className="text-sm text-gray-500">Range</p>
                      <p className="text-xl font-bold">
                        {stats.bodyFatStats.minBodyFat} - {stats.bodyFatStats.maxBodyFat}%
                      </p>
                    </div>
                  </div>
                </>
              )}
            </>
          )}

          {/* Chart */}
          <ResponsiveChartContainer>
            <Line
              key={`weight-chart-${timeframe}-${sortedEntries.length}`}
              data={chartData}
              options={chartOptions}
            />
          </ResponsiveChartContainer>

          <div className="mt-4 text-sm text-gray-500">
            <p>This chart shows your weight and body fat history over time. Use the display selector to choose which metrics to show and the timeframe selector to focus on different periods. Hover over points to see details.</p>
          </div>
        </>
      )}
    </div>
  );
};

export default WeightHistoryChart;
