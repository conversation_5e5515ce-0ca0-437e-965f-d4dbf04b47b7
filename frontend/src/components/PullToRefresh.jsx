// frontend/src/components/PullToRefresh.jsx
import React, { useState, useEffect, useRef } from 'react';

const PullToRefresh = ({ onRefresh, children }) => {
  const [isPulling, setIsPulling] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const containerRef = useRef(null);
  const startYRef = useRef(0);
  const thresholdDistance = 80; // Distance in pixels to trigger refresh

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleTouchStart = (e) => {
      // Only enable pull-to-refresh when at the top of the page
      if (window.scrollY === 0) {
        startYRef.current = e.touches[0].clientY;
        setIsPulling(true);
      }
    };

    const handleTouchMove = (e) => {
      if (!isPulling) return;
      
      const currentY = e.touches[0].clientY;
      const distance = currentY - startYRef.current;
      
      // Only allow pulling down, not up
      if (distance > 0) {
        // Apply resistance to make the pull feel more natural
        const resistedDistance = Math.min(distance * 0.4, thresholdDistance * 1.5);
        setPullDistance(resistedDistance);
        
        // Prevent default scrolling behavior when pulling
        e.preventDefault();
      }
    };

    const handleTouchEnd = async () => {
      if (!isPulling) return;
      
      if (pullDistance >= thresholdDistance) {
        // Trigger refresh
        setIsRefreshing(true);
        setPullDistance(thresholdDistance); // Keep indicator visible during refresh
        
        try {
          await onRefresh();
        } catch (error) {
          console.error('Refresh failed:', error);
        } finally {
          setIsRefreshing(false);
          setPullDistance(0);
          setIsPulling(false);
        }
      } else {
        // Reset without refreshing
        setPullDistance(0);
        setIsPulling(false);
      }
    };

    container.addEventListener('touchstart', handleTouchStart, { passive: true });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd);

    return () => {
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
    };
  }, [isPulling, pullDistance, onRefresh]);

  return (
    <div ref={containerRef} className="relative">
      {/* Pull indicator */}
      <div 
        className="absolute left-0 right-0 flex justify-center items-center transition-transform duration-200 z-10"
        style={{ 
          transform: `translateY(${pullDistance - 60}px)`,
          opacity: pullDistance / thresholdDistance
        }}
      >
        <div className="bg-primary-100 rounded-full p-3 shadow-md">
          {isRefreshing ? (
            <div className="animate-spin h-6 w-6 border-2 border-primary-500 border-t-transparent rounded-full"></div>
          ) : (
            <i className={`fas fa-arrow-down text-primary-500 transform transition-transform ${pullDistance > thresholdDistance ? 'rotate-180' : ''}`}></i>
          )}
        </div>
      </div>
      
      {/* Content container */}
      <div 
        style={{ 
          transform: `translateY(${pullDistance}px)`,
          transition: isPulling ? 'none' : 'transform 0.3s ease-out'
        }}
      >
        {children}
      </div>
    </div>
  );
};

export default PullToRefresh;
