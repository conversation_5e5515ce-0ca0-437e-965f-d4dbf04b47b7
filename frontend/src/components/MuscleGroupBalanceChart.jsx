// frontend/src/components/MuscleGroupBalanceChart.jsx
import React, { useState, useEffect } from 'react';
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend
} from 'chart.js';
import { Radar } from 'react-chartjs-2';
import ResponsiveChartContainer from './ResponsiveChartContainer';
import api from '../services/api';

// Register ChartJS components
ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend
);

// Common muscle groups for strength training
const MUSCLE_GROUPS = [
  'chest',
  'back',
  'shoulders',
  'biceps',
  'triceps',
  'legs',
  'core',
  'glutes',
  'hamstrings',
  'quadriceps',
  'calves',
  'forearms'
];

// Display names for muscle groups (capitalized)
const MUSCLE_GROUP_DISPLAY_NAMES = MUSCLE_GROUPS.map(
  group => group.charAt(0).toUpperCase() + group.slice(1)
);

const MuscleGroupBalanceChart = ({ workouts }) => {
  const [chartData, setChartData] = useState(null);
  const [timeRange, setTimeRange] = useState('3months'); // Default to 3 months
  const [exerciseMuscleMap, setExerciseMuscleMap] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch exercise-muscle group mappings from the API
  useEffect(() => {
    const fetchMuscleGroupMappings = async () => {
      try {
        setLoading(true);

        // Use the API service to make the call
        const data = await api.getExerciseMuscleGroupMappings();

        // Convert the array of mappings to a lookup object
        // Each exercise can map to multiple muscle groups
        const muscleMap = {};
        if (Array.isArray(data)) {
          data.forEach(mapping => {
            if (mapping && mapping.exercise_name && Array.isArray(mapping.muscle_groups)) {
              muscleMap[mapping.exercise_name.toLowerCase()] = mapping.muscle_groups.map(mg => mg.toLowerCase());
            }
          });
        } else {
          throw new Error('Received invalid data format from API');
        }

        setExerciseMuscleMap(muscleMap);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching muscle group mappings:', err);
        setError('Failed to load muscle group data');
        setLoading(false);
      }
    };

    fetchMuscleGroupMappings();
  }, []);

  // Process workouts to count muscle group frequency
  useEffect(() => {
    if (!workouts || workouts.length === 0 || loading || Object.keys(exerciseMuscleMap).length === 0) return;

    // Filter workouts based on selected time range
    const cutoffDate = new Date();
    switch (timeRange) {
      case '1month':
        cutoffDate.setMonth(cutoffDate.getMonth() - 1);
        break;
      case '3months':
        cutoffDate.setMonth(cutoffDate.getMonth() - 3);
        break;
      case '6months':
        cutoffDate.setMonth(cutoffDate.getMonth() - 6);
        break;
      case '1year':
        cutoffDate.setFullYear(cutoffDate.getFullYear() - 1);
        break;
      default:
        cutoffDate.setMonth(cutoffDate.getMonth() - 3);
    }

    // Filter workouts by date
    const filteredWorkouts = workouts.filter(workout =>
      new Date(workout.date) >= cutoffDate
    );

    // Count frequency of each muscle group
    const muscleGroupCounts = {};

    // Initialize all muscle groups to 0
    Object.keys(exerciseMuscleMap).forEach(exerciseName => {
      const muscleGroups = exerciseMuscleMap[exerciseName];
      if (muscleGroups && Array.isArray(muscleGroups)) {
        muscleGroups.forEach(group => {
          if (group) {
            const lowerGroup = group.toLowerCase();
            muscleGroupCounts[lowerGroup] = muscleGroupCounts[lowerGroup] || 0;
          }
        });
      }
    });

    // Also initialize our predefined muscle groups
    MUSCLE_GROUPS.forEach(group => {
      muscleGroupCounts[group] = muscleGroupCounts[group] || 0;
    });

    // Process each workout and exercise
    filteredWorkouts.forEach(workout => {
      workout.exercises.forEach(exercise => {
        if (exercise.exercise_type === 'strength') {
          // Try to find the exercise in our mapping (case-insensitive)
          const exerciseName = exercise.name.toLowerCase();

          // Try exact match first
          let muscleGroups = exerciseMuscleMap[exerciseName];

          // If no exact match, try case-insensitive match
          if (!muscleGroups) {
            const matchingKey = Object.keys(exerciseMuscleMap).find(key =>
              key.toLowerCase() === exerciseName
            );

            if (matchingKey) {
              muscleGroups = exerciseMuscleMap[matchingKey];
            } else {
              // Try partial matching as a last resort
              const matchingKeys = Object.keys(exerciseMuscleMap).filter(key =>
                key.toLowerCase().includes(exerciseName) ||
                exerciseName.includes(key.toLowerCase())
              );

              if (matchingKeys.length > 0) {
                // Use the first match
                muscleGroups = exerciseMuscleMap[matchingKeys[0]];
              }
            }
          }

          // If we found muscle groups, increment their counts
          if (muscleGroups && Array.isArray(muscleGroups)) {
            muscleGroups.forEach(group => {
              if (group) {
                const lowerGroup = group.toLowerCase();
                if (muscleGroupCounts.hasOwnProperty(lowerGroup)) {
                  muscleGroupCounts[lowerGroup] += 1;
                }
              }
            });
          } else {
            console.log(`No muscle groups found for exercise: ${exercise.name}`);
          }
        }
      });
    });

    // Prepare data for Chart.js - use only muscle groups that have data
    const activeGroups = Object.keys(muscleGroupCounts).filter(group =>
      muscleGroupCounts[group] > 0
    );

    const activeGroupDisplayNames = activeGroups.map(
      group => group.charAt(0).toUpperCase() + group.slice(1)
    );

    // Sort by count (descending)
    const sortedGroups = [...activeGroups].sort((a, b) =>
      muscleGroupCounts[b] - muscleGroupCounts[a]
    );

    const sortedGroupNames = sortedGroups.map(
      group => group.charAt(0).toUpperCase() + group.slice(1)
    );

    const data = {
      labels: sortedGroupNames.length > 0 ? sortedGroupNames : MUSCLE_GROUP_DISPLAY_NAMES,
      datasets: [
        {
          label: 'Exercises Per Muscle Group',
          data: sortedGroups.length > 0
            ? sortedGroups.map(group => muscleGroupCounts[group])
            : MUSCLE_GROUPS.map(group => muscleGroupCounts[group]),
          backgroundColor: 'rgba(99, 102, 241, 0.2)',
          borderColor: 'rgb(99, 102, 241)',
          borderWidth: 2,
          pointBackgroundColor: 'rgb(99, 102, 241)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgb(99, 102, 241)'
        }
      ]
    };

    setChartData(data);
  }, [workouts, timeRange, exerciseMuscleMap, loading]);

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 10,
          font: {
            size: 10
          }
        }
      },
      title: {
        display: true,
        text: 'Muscle Group Balance',
        font: {
          size: 14
        }
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            return `${context.label}: ${context.raw} exercises`;
          }
        }
      }
    },
    scales: {
      r: {
        angleLines: {
          display: true
        },
        suggestedMin: 0,
        ticks: {
          font: {
            size: 9
          },
          backdropPadding: 3
        },
        pointLabels: {
          font: {
            size: 10
          }
        }
      }
    }
  };

  return (
    <div className="bg-white shadow-sm rounded-lg p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Muscle Group Balance</h2>

        <div className="flex space-x-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            disabled={loading}
          >
            <option value="1month">Last Month</option>
            <option value="3months">Last 3 Months</option>
            <option value="6months">Last 6 Months</option>
            <option value="1year">Last Year</option>
          </select>
        </div>
      </div>

      {loading ? (
        <ResponsiveChartContainer className="flex items-center justify-center bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500 mb-2"></div>
            <p className="text-gray-500">Loading muscle group data...</p>
          </div>
        </ResponsiveChartContainer>
      ) : error ? (
        <ResponsiveChartContainer className="flex items-center justify-center bg-red-50 rounded-lg">
          <p className="text-red-500">{error}</p>
        </ResponsiveChartContainer>
      ) : chartData ? (
        <ResponsiveChartContainer className="flex items-center justify-center">
          <div className="w-full max-w-md h-full">
            <Radar data={chartData} options={chartOptions} />
          </div>
        </ResponsiveChartContainer>
      ) : (
        <ResponsiveChartContainer className="flex items-center justify-center bg-gray-50 rounded-lg">
          <p className="text-gray-500">No strength exercise data available for the selected time period</p>
        </ResponsiveChartContainer>
      )}

      <div className="mt-4 text-sm text-gray-500">
        <p>This chart shows the distribution of exercises across different muscle groups based on your workout history.</p>
        <p className="mt-1">Each exercise may target multiple muscle groups according to the exercise database.</p>
      </div>
    </div>
  );
};

export default MuscleGroupBalanceChart;
