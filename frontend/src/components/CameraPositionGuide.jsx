// frontend/src/components/CameraPositionGuide.jsx
import React from 'react';

const CameraPositionGuide = () => {
  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <h3 className="text-xl font-semibold text-gray-800 mb-4">Camera Positioning Guide</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h4 className="font-medium text-lg text-gray-700 mb-2">Side View (Recommended)</h4>
          <div className="relative bg-gray-100 rounded-lg p-4 h-64 flex items-center justify-center">
            {/* Camera */}
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
              <div className="w-8 h-8 bg-gray-700 rounded-lg"></div>
              <div className="w-2 h-16 bg-gray-700 mx-auto"></div>
              <div className="w-8 h-1 bg-gray-700 mx-auto"></div>
              <div className="text-xs text-gray-700 text-center mt-1">Camera</div>
            </div>
            
            {/* Person */}
            <div className="relative">
              {/* Head */}
              <div className="w-8 h-8 bg-blue-500 rounded-full mx-auto"></div>
              
              {/* Body */}
              <div className="w-4 h-16 bg-blue-500 mx-auto"></div>
              
              {/* Arms */}
              <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-16 h-2 bg-blue-500"></div>
              
              {/* Legs */}
              <div className="w-12 mx-auto flex justify-between">
                <div className="w-2 h-12 bg-blue-500"></div>
                <div className="w-2 h-12 bg-blue-500"></div>
              </div>
              
              {/* Barbell */}
              <div className="absolute top-6 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-gray-700"></div>
              
              <div className="text-xs text-gray-700 text-center mt-2">You</div>
            </div>
            
            {/* Distance indicator */}
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-3/4 flex justify-between">
              <div className="w-1 h-4 bg-gray-400"></div>
              <div className="w-1 h-4 bg-gray-400"></div>
              <div className="text-xs text-gray-600 absolute -bottom-5 w-full text-center">6-8 feet distance</div>
            </div>
          </div>
          <div className="mt-2 text-sm text-gray-600">
            <ul className="list-disc pl-5">
              <li>Best for analyzing knee, hip, and back angles</li>
              <li>Shows depth and forward lean clearly</li>
              <li>Place camera at hip height</li>
            </ul>
          </div>
        </div>
        
        <div>
          <h4 className="font-medium text-lg text-gray-700 mb-2">Front View (Optional)</h4>
          <div className="relative bg-gray-100 rounded-lg p-4 h-64 flex items-center justify-center">
            {/* Camera */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
              <div className="w-8 h-8 bg-gray-700 rounded-lg"></div>
              <div className="w-2 h-16 bg-gray-700 mx-auto"></div>
              <div className="w-8 h-1 bg-gray-700 mx-auto"></div>
              <div className="text-xs text-gray-700 text-center mt-1">Camera</div>
            </div>
            
            {/* Person */}
            <div className="relative mb-20">
              {/* Head */}
              <div className="w-8 h-8 bg-blue-500 rounded-full mx-auto"></div>
              
              {/* Body */}
              <div className="w-4 h-16 bg-blue-500 mx-auto"></div>
              
              {/* Arms */}
              <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-16 h-2 bg-blue-500"></div>
              
              {/* Legs */}
              <div className="w-12 mx-auto flex justify-between">
                <div className="w-2 h-12 bg-blue-500"></div>
                <div className="w-2 h-12 bg-blue-500"></div>
              </div>
              
              {/* Barbell */}
              <div className="absolute top-6 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-gray-700"></div>
              
              <div className="text-xs text-gray-700 text-center mt-2">You</div>
            </div>
            
            {/* Distance indicator */}
            <div className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3/4 flex flex-col justify-between">
              <div className="h-1 w-4 bg-gray-400"></div>
              <div className="h-1 w-4 bg-gray-400"></div>
              <div className="text-xs text-gray-600 absolute -left-16 w-14 text-right top-1/2 transform -translate-y-1/2">6-8 feet</div>
            </div>
          </div>
          <div className="mt-2 text-sm text-gray-600">
            <ul className="list-disc pl-5">
              <li>Good for analyzing knee tracking</li>
              <li>Shows if knees cave inward (valgus)</li>
              <li>Place camera at knee height</li>
            </ul>
          </div>
        </div>
      </div>
      
      <div className="mt-4 p-3 bg-yellow-50 border-l-4 border-yellow-400 rounded">
        <p className="text-sm text-yellow-800">
          <strong>Pro Tip:</strong> For the most comprehensive analysis, record two separate videos - one from the side and one from the front. This allows for complete assessment of your squat form.
        </p>
      </div>
    </div>
  );
};

export default CameraPositionGuide;
