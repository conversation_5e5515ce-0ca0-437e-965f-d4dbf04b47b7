// frontend/src/components/SocialFeed.jsx
import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import api from '../services/api';

const SocialFeed = () => {
  const [socialWorkouts, setSocialWorkouts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchSocialFeed = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await api.getSocialFeed();
        setSocialWorkouts(data);
      } catch (err) {
        console.error('Error fetching social feed:', err);
        setError('Failed to load social feed');
      } finally {
        setLoading(false);
      }
    };

    fetchSocialFeed();
  }, []);

  // Helper function to get a summary of exercises
  const getExerciseSummary = (workout) => {
    if (!workout.exercises || workout.exercises.length === 0) {
      return 'No exercises recorded';
    }

    if (workout.exercises.length === 1) {
      return `1 exercise: ${workout.exercises[0].name}`;
    }

    return `${workout.exercises.length} exercises`;
  };

  const handleUseWorkout = async (workout) => {
    try {
      // Create a new workout based on the selected workout
      const workoutData = {
        exercises: workout.exercises.map(exercise => {
          // Get the first set's data if available for suggested reps/sets
          const firstSet = exercise.sets && exercise.sets.length > 0 ? exercise.sets[0] : null;

          return {
            name: exercise.name,
            type: exercise.exercise_type,
            // Include additional fields expected by the backend
            sets: firstSet ? "3" : undefined,  // Default to 3 sets if there's at least one set
            reps: firstSet && firstSet.reps ? String(firstSet.reps) : undefined,
            description: `${exercise.name} from ${workout.username}'s workout`,
            instructions: ["Follow proper form"]
          };
        }),
        description: `Based on ${workout.username}'s workout from ${format(new Date(workout.date), 'MMM d, yyyy')}`,
        status: "in_progress" // Explicitly set the status to in_progress
      };

      const newWorkout = await api.createWorkoutFromGenerated(workoutData);

      // Navigate to the active workout page with the new workout ID
      navigate('/active-workout', {
        state: {
          workoutCreated: true,
          workoutId: newWorkout.id
        }
      });
    } catch (err) {
      console.error('Error using workout:', err);
      setError('Failed to use this workout');
    }
  };

  if (loading) {
    return (
      <div className="animate-pulse p-4">
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div className="h-24 bg-gray-100 rounded mb-2"></div>
        <div className="h-24 bg-gray-100 rounded mb-2"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-4 rounded-lg text-red-600">
        <p>{error}</p>
      </div>
    );
  }

  if (socialWorkouts.length === 0) {
    return (
      <div className="bg-gray-50 p-4 rounded-lg text-center">
        <p className="text-gray-600">No recent workouts from other users.</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="bg-indigo-50 px-4 py-3 border-b border-indigo-100">
        <h2 className="text-lg font-semibold text-indigo-800">Community Workouts</h2>
      </div>

      <div className="divide-y divide-gray-100 max-h-[600px] overflow-y-auto">
        {socialWorkouts.map((workout) => (
          <div key={workout.id} className="p-4 hover:bg-gray-50">
            <div className="flex justify-between items-start mb-2">
              <div>
                <h3 className="font-medium text-gray-900">
                  {workout.username} completed a workout
                </h3>
                <p className="text-xs text-gray-500">
                  {format(new Date(workout.date), 'MMM d, yyyy')}
                </p>
              </div>
            </div>

            <div className="text-dark-600 text-sm mb-3 flex items-center">
              <i className="fas fa-dumbbell mr-2 text-primary-500"></i>
              {getExerciseSummary(workout)}
            </div>

            {workout.description && (
              <p className="text-dark-500 text-xs mb-3 italic">
                "{workout.description}"
              </p>
            )}

            <div className="flex space-x-2 mt-2">
              <Link
                to={`/workout/${workout.id}`}
                className="text-xs text-indigo-600 hover:text-indigo-800 px-2 py-1 border border-indigo-200 rounded-md"
              >
                See Workout <i className="fas fa-eye ml-1"></i>
              </Link>

              <button
                onClick={() => handleUseWorkout(workout)}
                className="text-xs text-green-600 hover:text-green-800 px-2 py-1 border border-green-200 rounded-md"
              >
                Use Workout <i className="fas fa-play ml-1"></i>
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SocialFeed;
