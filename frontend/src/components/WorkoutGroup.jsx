// frontend/src/components/WorkoutGroup.jsx
import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { format } from 'date-fns';

const WorkoutGroup = ({ 
  title, 
  workouts, 
  getExerciseSummary, 
  selectedWorkouts, 
  onSelectWorkout,
  expanded = false
}) => {
  const [isExpanded, setIsExpanded] = useState(expanded);
  
  if (!workouts || workouts.length === 0) {
    return null;
  }
  
  return (
    <div className="mb-6">
      {/* Group Header */}
      <div 
        className="flex justify-between items-center mb-3 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <h3 className="text-lg font-semibold text-gray-700 flex items-center">
          <span>{title}</span>
          <span className="ml-2 text-sm text-gray-500">({workouts.length})</span>
        </h3>
        <button className="text-gray-500 hover:text-gray-700">
          <i className={`fas fa-chevron-${isExpanded ? 'up' : 'down'}`}></i>
        </button>
      </div>
      
      {/* Workout Cards */}
      {isExpanded && (
        <div className="grid gap-3 sm:grid-cols-1 md:grid-cols-2">
          {workouts.map((workout) => (
            <div
              key={workout.id}
              className="card overflow-hidden hover:shadow-md transition-shadow duration-200"
            >
              <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedWorkouts.includes(workout.id)}
                      onChange={() => onSelectWorkout(workout.id)}
                      className="form-checkbox mr-3"
                      onClick={(e) => e.stopPropagation()}
                    />
                    <h2 className="text-base font-semibold text-dark-800">
                      {format(new Date(workout.date), 'EEE, MMM d, yyyy')}
                    </h2>
                  </div>
                  <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium">
                    Completed
                  </span>
                </div>

                <div className="text-dark-600 text-sm mb-2 flex items-center">
                  <i className="fas fa-dumbbell mr-2 text-primary-500"></i>
                  {getExerciseSummary(workout)}
                </div>

                {workout.description && (
                  <p className="text-dark-500 text-xs truncate mb-2">
                    <i className="fas fa-quote-left mr-1 text-gray-400"></i>
                    {workout.description}
                  </p>
                )}

                <Link
                  to={`/workout/${workout.id}`}
                  className="block w-full text-center py-1 mt-1 text-primary-600 hover:text-primary-800 text-sm font-medium border-t border-gray-100"
                >
                  View Details <i className="fas fa-chevron-right ml-1 text-xs"></i>
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default WorkoutGroup;
