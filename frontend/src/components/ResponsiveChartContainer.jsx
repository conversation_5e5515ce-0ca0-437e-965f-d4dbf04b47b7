// frontend/src/components/ResponsiveChartContainer.jsx
import React from 'react';

/**
 * A responsive container for charts that adjusts height based on screen size
 * and maintains a proper aspect ratio for better visualization.
 */
const ResponsiveChartContainer = ({ children, className = '' }) => {
  return (
    <div className={`
      w-full 
      h-64 sm:h-80 md:h-96 
      ${className}
    `}>
      {children}
    </div>
  );
};

export default ResponsiveChartContainer;
