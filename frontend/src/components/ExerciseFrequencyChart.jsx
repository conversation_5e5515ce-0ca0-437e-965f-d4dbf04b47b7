// frontend/src/components/ExerciseFrequencyChart.jsx
import React, { useState, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import ResponsiveChartContainer from './ResponsiveChartContainer';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const ExerciseFrequencyChart = ({ workouts }) => {
  const [chartData, setChartData] = useState(null);
  const [exerciseType, setExerciseType] = useState('strength'); // Default to strength exercises
  const [displayCount, setDisplayCount] = useState(10); // Number of exercises to display

  // Process workouts to count exercise frequency
  useEffect(() => {
    if (!workouts || workouts.length === 0) return;

    // Count frequency of each exercise
    const exerciseCounts = {};

    workouts.forEach(workout => {
      workout.exercises.forEach(exercise => {
        if (exerciseType === 'all' || exercise.exercise_type === exerciseType) {
          exerciseCounts[exercise.name] = (exerciseCounts[exercise.name] || 0) + 1;
        }
      });
    });

    // Convert to array and sort by frequency
    const sortedExercises = Object.entries(exerciseCounts)
      .sort((a, b) => b[1] - a[1]) // Sort by count (descending)
      .slice(0, displayCount); // Take top N

    if (sortedExercises.length > 0) {
      // Prepare data for Chart.js
      const data = {
        labels: sortedExercises.map(([name]) => name),
        datasets: [
          {
            label: 'Times Performed',
            data: sortedExercises.map(([, count]) => count),
            backgroundColor: 'rgba(147, 51, 234, 0.7)', // Purple
            borderColor: 'rgb(147, 51, 234)',
            borderWidth: 1
          }
        ]
      };

      setChartData(data);
    } else {
      setChartData(null);
    }
  }, [workouts, exerciseType, displayCount]);

  const chartOptions = {
    indexAxis: 'y', // Horizontal bar chart
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 10,
          font: {
            size: 10
          }
        }
      },
      title: {
        display: true,
        text: 'Most Frequent Exercises',
        font: {
          size: 14
        }
      }
    },
    scales: {
      x: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Times Performed',
          font: {
            size: 10
          }
        },
        ticks: {
          font: {
            size: 9
          }
        }
      },
      y: {
        ticks: {
          font: {
            size: 9
          },
          // Limit label length on mobile
          callback: function(value, index, values) {
            const label = this.getLabelForValue(value);
            // On small screens, truncate long exercise names
            const maxLength = window.innerWidth < 768 ? 15 : 25;
            return label.length > maxLength ? label.substring(0, maxLength) + '...' : label;
          }
        }
      }
    }
  };

  return (
    <div className="bg-white shadow-sm rounded-lg p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Exercise Frequency</h2>

        <div className="flex space-x-2">
          <select
            value={exerciseType}
            onChange={(e) => setExerciseType(e.target.value)}
            className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          >
            <option value="strength">Strength</option>
            <option value="cardio">Cardio</option>
            <option value="flexibility">Flexibility</option>
            <option value="all">All Types</option>
          </select>

          <select
            value={displayCount}
            onChange={(e) => setDisplayCount(Number(e.target.value))}
            className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          >
            <option value="5">Top 5</option>
            <option value="10">Top 10</option>
            <option value="15">Top 15</option>
          </select>
        </div>
      </div>

      {chartData ? (
        <ResponsiveChartContainer>
          <Bar data={chartData} options={chartOptions} />
        </ResponsiveChartContainer>
      ) : (
        <ResponsiveChartContainer className="flex items-center justify-center bg-gray-50 rounded-lg">
          <p className="text-gray-500">No exercise data available for the selected type</p>
        </ResponsiveChartContainer>
      )}
    </div>
  );
};

export default ExerciseFrequencyChart;
