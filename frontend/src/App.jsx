// frontend/src/App.jsx
import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import Navbar from './components/Navbar';
import Dashboard from './pages/Dashboard';
import Login from './pages/Login';
import Register from './pages/Register';
import WorkoutDetail from './pages/WorkoutDetail';
import ActiveWorkout from './pages/ActiveWorkout';
import WorkoutComplete from './pages/WorkoutComplete';
import WorkoutStatistics from './pages/WorkoutStatistics';
import WorkoutFinder from './pages/WorkoutFinder';
import GenerateWorkout from './pages/GenerateWorkout';
import FormAnalysis from './pages/FormAnalysis';
import Progress from './pages/Progress';
import Leaderboard from './pages/Leaderboard';
import WeightTracking from './pages/WeightTracking';
import './index.css';

// Protected route component
const ProtectedRoute = ({ element }) => {
  const { token } = useAuth();

  if (!token) {
    return <Navigate to="/login" />;
  }

  return element;
};

// Scroll to top component
const ScrollToTop = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
};

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Navbar />
          <main className="pt-6 pb-12 px-4 sm:px-6 md:px-8 max-w-7xl mx-auto">
            <ScrollToTop />
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/active-workout" element={<ProtectedRoute element={<ActiveWorkout />} />} />
              <Route path="/workout/:id" element={<ProtectedRoute element={<WorkoutDetail />} />} />
              <Route path="/workout-complete/:id" element={<ProtectedRoute element={<WorkoutComplete />} />} />
              <Route path="/statistics" element={<ProtectedRoute element={<WorkoutStatistics />} />} />
              <Route path="/workout-finder" element={<ProtectedRoute element={<WorkoutFinder />} />} />
              <Route path="/generate-workout" element={<ProtectedRoute element={<GenerateWorkout />} />} />
              <Route path="/form-analysis" element={<ProtectedRoute element={<FormAnalysis />} />} />
              <Route path="/progress" element={<ProtectedRoute element={<Progress />} />} />
              <Route path="/leaderboard" element={<ProtectedRoute element={<Leaderboard />} />} />
              <Route path="/weight-tracking" element={<ProtectedRoute element={<WeightTracking />} />} />
              <Route path="/" element={<ProtectedRoute element={<Dashboard />} />} />
            </Routes>
          </main>
          <footer className="bg-white border-t border-gray-200 py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
              <p className="text-center text-gray-500 text-sm">
                &copy; {new Date().getFullYear()} FitTrack. All rights reserved.
              </p>
            </div>
          </footer>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;