@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --page-background: #f9fafb;
  --card-background: #ffffff;
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --card-border-radius: 1rem;
  --input-border-radius: 0.5rem;
  --button-border-radius: 0.5rem;
  --transition-speed: 0.2s;
}

@layer base {
  html {
    @apply text-dark-800;
  }

  body {
    margin: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Can<PERSON>ell', '<PERSON><PERSON>', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    @apply bg-gray-50;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
  }

  a {
    @apply transition-colors duration-200;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent
           rounded-md shadow-sm text-sm font-medium transition-all duration-200;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:outline-none focus:ring-2
           focus:ring-offset-2 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700 focus:outline-none focus:ring-2
           focus:ring-offset-2 focus:ring-secondary-500;
  }

  .btn-outline {
    @apply border-gray-300 bg-white text-gray-700 hover:bg-gray-50;
  }

  .card {
    @apply bg-white rounded-xl shadow-card hover:shadow-card-hover transition-shadow duration-200 border border-gray-100;
  }

  .form-input {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500
           focus:ring-primary-500 sm:text-sm;
  }

  .form-select {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500
           focus:ring-primary-500 sm:text-sm;
  }

  .form-checkbox {
    @apply rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-500
           focus:ring-primary-500;
  }
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .container {
    @apply px-4;
  }

  .card {
    @apply rounded-lg;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 1000px;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.2s ease-out forwards;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out forwards;
}

/* Hamburger menu styling */
.hamburger-menu {
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}