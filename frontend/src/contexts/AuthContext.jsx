// frontend/src/contexts/AuthContext.jsx
import React, { createContext, useState, useContext, useEffect } from 'react';
import axios from 'axios';
import api from '../services/api';

const AuthContext = createContext();
export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [token, setToken] = useState(localStorage.getItem('token'));
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const debugLog = (msg, ...args) => console.log(`[AuthContext Debug] ${msg}`, ...args);

  const fetchUserData = async (retries = 2) => {
    try {
      debugLog('Attempting to fetch user data');
      
      // Get fresh token directly from localStorage
      const currentToken = localStorage.getItem('token');
      
      if (!currentToken) {
        debugLog('No token available, cannot fetch user data');
        setLoading(false);
        setUser(null);
        return null;
      }
      
      const res = await fetch('/users/me/', {
        headers: {
          Authorization: `Bearer ${currentToken}`,
          'Content-Type': 'application/json',
        },
      });
      debugLog('Fetch user response status:', res.status);

      if (!res.ok) {
        const errText = await res.text();
        debugLog('Fetch user error:', errText);
        if (res.status === 401 && retries > 0) return fetchUserData(retries - 1);
        throw new Error(errText || 'Failed to fetch user data');
      }

      const data = await res.json();
      debugLog('User data fetched:', data);
      setUser(data);
      setError(null);
      return data;
    } catch (err) {
      debugLog('Error fetching user data:', err);
      setError(err.message);
      logout();
      return null;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    debugLog('Initial token:', token);
    if (token) {
      axios.defaults.headers.common.Authorization = `Bearer ${token}`;
      fetchUserData();
    } else {
      delete axios.defaults.headers.common.Authorization;
      setUser(null);
      setLoading(false);
    }
  }, [token]);

  const login = async (username, password) => {
    try {
      debugLog('Login attempt for:', username);
      setLoading(true);
      setError(null);

      const res = await api.loginUser(username, password);
      debugLog('Login response:', res);

      if (!res.access_token) throw new Error('No access token received');

      const newToken = res.access_token;
      localStorage.setItem('token', newToken);
      setToken(newToken);
      return true;
    } catch (err) {
      debugLog('Login error:', err);
      setError(err.message);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const register = async (username, password) => {
    try {
      debugLog('Registration attempt for:', username);
      setLoading(true);
      setError(null);
      await api.registerUser(username, password);
      return true;
    } catch (err) {
      debugLog('Registration error:', err);
      setError(err.message);
      // Throw the error so the component can handle it
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    debugLog('Logout initiated');
    localStorage.removeItem('token');
    setToken(null);
    setUser(null);
    setError(null);
    delete axios.defaults.headers.common.Authorization;
  };

  return (
    <AuthContext.Provider
      value={{ token, user, loading, error, login, register, logout }}
    >
      {children}
    </AuthContext.Provider>
  );
};