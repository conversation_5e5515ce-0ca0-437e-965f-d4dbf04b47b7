// frontend/src/pages/WorkoutStatistics.jsx
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { format, parseISO, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay } from 'date-fns';
import api from '../services/api';
import StrengthProgressChart from '../components/StrengthProgressChart';
import WorkoutVolumeChart from '../components/WorkoutVolumeChart';
import ExerciseFrequencyChart from '../components/ExerciseFrequencyChart';
import MuscleGroupBalanceChart from '../components/MuscleGroupBalanceChart';
import MobileOrientationHelper from '../components/MobileOrientationHelper';

const WorkoutStatistics = () => {
  const [workouts, setWorkouts] = useState([]);
  const [personalRecords, setPersonalRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  // Fetch all completed workouts and personal records
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch workouts
        const allWorkouts = await api.getWorkouts();
        const completedWorkouts = allWorkouts.filter(w =>
          w.status === 'completed' || !w.status
        );
        setWorkouts(completedWorkouts);

        // Fetch personal records
        const records = await api.getPersonalRecords();
        setPersonalRecords(records || []);

      } catch (err) {
        setError('Failed to load workout data');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Calculate statistics
  const calculateStats = () => {
    if (!workouts.length) return null;

    // Basic stats
    const totalWorkouts = workouts.length;

    // Get unique workout days
    const uniqueDays = new Set(workouts.map(w => format(new Date(w.date), 'yyyy-MM-dd')));
    const activeDays = uniqueDays.size;

    // Calculate exercise type distribution
    const exerciseTypes = {};
    let totalExercises = 0;

    workouts.forEach(workout => {
      (workout.exercises || []).forEach(exercise => {
        const type = exercise.exercise_type || 'other';
        exerciseTypes[type] = (exerciseTypes[type] || 0) + 1;
        totalExercises++;
      });
    });

    // Calculate most frequent exercise type
    let mostFrequentType = 'none';
    let maxCount = 0;

    Object.entries(exerciseTypes).forEach(([type, count]) => {
      if (count > maxCount) {
        mostFrequentType = type;
        maxCount = count;
      }
    });

    // Calculate workout frequency by month (last 6 months)
    const today = new Date();
    const monthlyWorkouts = [];

    for (let i = 5; i >= 0; i--) {
      const targetMonth = new Date(today.getFullYear(), today.getMonth() - i, 1);
      const monthStart = startOfMonth(targetMonth);
      const monthEnd = endOfMonth(targetMonth);

      const workoutsInMonth = workouts.filter(w => {
        const workoutDate = new Date(w.date);
        return workoutDate >= monthStart && workoutDate <= monthEnd;
      });

      monthlyWorkouts.push({
        month: format(targetMonth, 'MMM yyyy'),
        count: workoutsInMonth.length
      });
    }

    // Calculate workout streak
    const sortedDates = workouts
      .map(w => new Date(w.date))
      .sort((a, b) => b - a); // Sort descending

    return {
      totalWorkouts,
      activeDays,
      exerciseTypes,
      totalExercises,
      mostFrequentType,
      monthlyWorkouts,
      averageExercisesPerWorkout: totalExercises / totalWorkouts
    };
  };

  // Generate workout calendar data
  const generateCalendarData = () => {
    if (!workouts.length) return [];

    // Get current month days
    const today = new Date();
    const monthStart = startOfMonth(today);
    const monthEnd = endOfMonth(today);
    const days = eachDayOfInterval({ start: monthStart, end: monthEnd });

    // Map workouts to days
    return days.map(day => {
      const workoutsOnDay = workouts.filter(w => {
        const workoutDate = parseISO(w.date);
        return isSameDay(workoutDate, day);
      });

      return {
        date: day,
        hasWorkout: workoutsOnDay.length > 0,
        workoutCount: workoutsOnDay.length
      };
    });
  };

  const stats = calculateStats();
  const calendarData = generateCalendarData();

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        <span className="ml-3 text-gray-600">Loading statistics...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Workout Statistics Dashboard</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {workouts.length === 0 ? (
        <div className="bg-white shadow-sm rounded-lg p-8 text-center">
          <h2 className="text-xl font-semibold mb-4">No Workout Data Yet</h2>
          <p className="text-gray-600 mb-6">Complete some workouts to see your statistics and progress.</p>
          <button
            onClick={() => navigate('/active-workout')}
            className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700"
          >
            Start Your First Workout
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Summary Card */}
          <div className="bg-white shadow-sm rounded-lg p-6 col-span-full">
            <h2 className="text-xl font-semibold mb-4">Workout Summary</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-indigo-50 p-4 rounded-lg text-center">
                <div className="text-3xl font-bold text-indigo-600">{stats?.totalWorkouts || 0}</div>
                <div className="text-sm text-gray-600">Total Workouts</div>
              </div>

              <div className="bg-green-50 p-4 rounded-lg text-center">
                <div className="text-3xl font-bold text-green-600">{stats?.activeDays || 0}</div>
                <div className="text-sm text-gray-600">Active Days</div>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg text-center">
                <div className="text-3xl font-bold text-purple-600">{stats?.totalExercises || 0}</div>
                <div className="text-sm text-gray-600">Total Exercises</div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg text-center">
                <div className="text-3xl font-bold text-blue-600">
                  {stats?.averageExercisesPerWorkout.toFixed(1) || 0}
                </div>
                <div className="text-sm text-gray-600">Avg Exercises/Workout</div>
              </div>
            </div>
          </div>

          {/* Exercise Type Distribution */}
          <div className="bg-white shadow-sm rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Exercise Type Distribution</h2>
            {stats?.exerciseTypes && Object.keys(stats.exerciseTypes).length > 0 ? (
              <div className="space-y-3">
                {Object.entries(stats.exerciseTypes).map(([type, count]) => (
                  <div key={type} className="flex items-center">
                    <div className="w-24 text-gray-700 capitalize">{type}</div>
                    <div className="flex-1 mx-2">
                      <div className="bg-gray-200 rounded-full h-4 overflow-hidden">
                        <div
                          className="bg-indigo-500 h-4"
                          style={{ width: `${(count / stats.totalExercises) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="text-gray-600 text-sm w-12 text-right">
                      {count} ({Math.round((count / stats.totalExercises) * 100)}%)
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No exercise data available</p>
            )}
          </div>

          {/* Monthly Workout Frequency */}
          <div className="bg-white shadow-sm rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Monthly Workout Frequency</h2>
            {stats?.monthlyWorkouts && stats.monthlyWorkouts.length > 0 ? (
              <div className="h-64 flex items-end space-x-2">
                {stats.monthlyWorkouts.map((item) => (
                  <div key={item.month} className="flex flex-col items-center flex-1">
                    <div className="w-full bg-indigo-100 rounded-t-sm" style={{
                      height: `${Math.max(item.count * 15, 4)}px`,
                      minHeight: '4px'
                    }}>
                      <div
                        className="bg-indigo-500 w-full h-full rounded-t-sm"
                        title={`${item.count} workouts`}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-600 mt-2 rotate-45 origin-top-left">
                      {item.month}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No monthly data available</p>
            )}
          </div>

          {/* This Month's Activity */}
          <div className="bg-white shadow-sm rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">This Month's Activity</h2>
            <div className="grid grid-cols-7 gap-1">
              {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, i) => (
                <div key={i} className="text-center text-xs text-gray-500 font-medium">
                  {day}
                </div>
              ))}

              {calendarData.map((day, i) => (
                <div
                  key={i}
                  className={`aspect-square rounded-full flex items-center justify-center text-xs
                    ${day.hasWorkout
                      ? 'bg-indigo-500 text-white'
                      : 'bg-gray-100 text-gray-400'}`}
                  title={day.hasWorkout
                    ? `${day.workoutCount} workout(s) on ${format(day.date, 'MMM d')}`
                    : format(day.date, 'MMM d')}
                >
                  {format(day.date, 'd')}
                </div>
              ))}
            </div>
          </div>

          {/* Strength Progress Section */}
          <div className="col-span-full">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">Strength Progress Analytics</h2>
            <p className="text-gray-600 mb-6">Track your strength improvements and workout patterns over time.</p>

            {/* Mobile orientation helper */}
            <MobileOrientationHelper />
          </div>

          {/* Charts Section - Each chart takes full width on mobile for better visibility */}
          <div className="col-span-full mb-8">
            <StrengthProgressChart workouts={workouts} />
          </div>

          <div className="col-span-full mb-8">
            <WorkoutVolumeChart workouts={workouts} />
          </div>

          <div className="col-span-full mb-8">
            <ExerciseFrequencyChart workouts={workouts} />
          </div>

          <div className="col-span-full mb-8">
            <MuscleGroupBalanceChart workouts={workouts} />
          </div>

          {/* Personal Records */}
          <div className="bg-white shadow-sm rounded-lg p-6 col-span-full">
            <h2 className="text-xl font-semibold mb-4">Personal Records</h2>
            {personalRecords.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Exercise
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Value
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {personalRecords.map((record, i) => (
                      <tr key={i} className="hover:bg-gray-50">
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="font-medium text-gray-900">{record.exercise_name}</div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            {record.exercise_type}
                          </span>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-right text-sm">
                          <div className="font-bold text-gray-900">
                            {record.value} {record.unit}
                          </div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                          {format(new Date(record.date), 'MMM d, yyyy')}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No personal records yet</p>
            )}
          </div>

          {/* Workout History Link */}
          <div className="col-span-full text-center mt-4">
            <button
              onClick={() => navigate('/')}
              className="text-indigo-600 hover:text-indigo-800 font-medium"
            >
              View Full Workout History
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default WorkoutStatistics;
