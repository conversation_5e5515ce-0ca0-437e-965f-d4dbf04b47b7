// frontend/src/pages/WorkoutComplete.jsx
import React, { useState, useEffect } from 'react';
import { useParams, Link } from 'react-router-dom';
import { format } from 'date-fns';
import api from '../services/api';
import PersonalRecordCelebration from '../components/PersonalRecordCelebration';
import { formatDuration } from '../utils/timeUtils';

const WorkoutComplete = () => {
  const { id } = useParams();
  const [workout, setWorkout] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [analysisLoading, setAnalysisLoading] = useState(false);
  const [analysis, setAnalysis] = useState(null);
  const [personalRecords, setPersonalRecords] = useState([]);
  const [showCelebration, setShowCelebration] = useState(false);
  const [currentRecord, setCurrentRecord] = useState(null);
  const [workoutScore, setWorkoutScore] = useState(null);

  // Debug logging function
  const debugLog = (message, ...args) => {
    console.log(`[WorkoutComplete Debug] ${message}`, ...args);
  };

  // Fetch workout data
  const fetchWorkout = async () => {
    try {
      debugLog(`Fetching workout details for ID: ${id}`);
      setLoading(true);
      setError('');

      const workoutData = await api.getWorkout(id);
      debugLog('Workout data fetched:', workoutData);

      // Ensure workout is marked as completed
      if (workoutData.status !== 'completed') {
        debugLog('Workout not completed, marking as completed');
        const updatedWorkout = await api.completeWorkout(id, workoutData.description);
        setWorkout(updatedWorkout);
      } else {
        setWorkout(workoutData);
      }

      return workoutData;
    } catch (err) {
      debugLog('Error fetching workout:', err);
      setError('Failed to load workout details. Please try again later.');
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Fetch workout analysis
  const fetchAnalysis = async () => {
    try {
      debugLog('Attempting to fetch workout analysis');
      setAnalysisLoading(true);

      const analysisData = await api.getWorkoutAnalysis(id);
      debugLog('Analysis data received:', analysisData);

      if (analysisData) {
        setAnalysis(analysisData);
      } else {
        throw new Error('No analysis data returned');
      }
    } catch (analysisError) {
      debugLog('Error fetching analysis:', analysisError);
      // Keep this non-fatal - we'll show a generate button instead
    } finally {
      setAnalysisLoading(false);
    }
  };

  // Fetch workout score
  const fetchWorkoutScore = async () => {
    try {
      debugLog('Fetching workout score');
      const score = await api.getWorkoutScore(id);
      debugLog('Workout score:', score);
      setWorkoutScore(score);
      return score;
    } catch (err) {
      debugLog('Error fetching workout score:', err);
      // Non-fatal error, just log it
      return null;
    }
  };

  // Fetch personal records and check for new ones from this workout
  const checkForPersonalRecords = async (workoutData) => {
    try {
      debugLog('Checking for personal records');

      // Fetch all personal records
      const records = await api.getPersonalRecords();
      setPersonalRecords(records);

      // Find records from this workout
      const newRecords = records.filter(record => record.workout_id === parseInt(id));

      debugLog('New personal records found:', newRecords);

      if (newRecords.length > 0) {
        // Show celebration for the first new record
        setCurrentRecord(newRecords[0]);
        setShowCelebration(true);

        // If there are multiple records, we'll show them one after another
        if (newRecords.length > 1) {
          let recordIndex = 1;

          const showNextRecord = () => {
            if (recordIndex < newRecords.length) {
              setCurrentRecord(newRecords[recordIndex]);
              setShowCelebration(true);
              recordIndex++;
            }
          };

          // Set up event listener for when celebration is closed
          const handleCelebrationClosed = () => {
            setShowCelebration(false);
            // Show next record after a short delay
            setTimeout(showNextRecord, 1000);
          };

          // Store the handler in a ref so we can access it later
          window.celebrationClosedHandler = handleCelebrationClosed;
        }
      }
    } catch (err) {
      debugLog('Error checking personal records:', err);
      // Non-fatal error, just log it
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    const loadData = async () => {
      const workoutData = await fetchWorkout();

      if (workoutData && workoutData.status === 'completed') {
        // Check for personal records and fetch workout score
        await Promise.all([
          checkForPersonalRecords(workoutData),
          fetchWorkoutScore()
        ]);
      }
    };

    loadData();
  }, [id]);

  // Manual analysis generation
  const handleGenerateAnalysis = () => {
    fetchAnalysis();
  };

  // formatDuration is now imported from timeUtils

  const calculateStrengthTotals = (exercises) => {
    const strengthExercises = exercises.filter(ex => ex.exercise_type === 'strength');
    let totalWeight = 0, totalReps = 0, totalSets = 0;
    strengthExercises.forEach(ex => {
      ex.sets.forEach(set => {
        if (set.reps && set.weight) {
          totalWeight += set.reps * set.weight;
          totalReps += set.reps;
        }
        totalSets++;
      });
    });
    return { totalWeight, totalReps, totalSets };
  };

  const calculateCardioTotals = (exercises) => {
    const cardioExercises = exercises.filter(ex => ex.exercise_type === 'cardio');
    let totalDistance = 0, totalDuration = 0, totalSets = 0;
    cardioExercises.forEach(ex => {
      ex.sets.forEach(set => {
        if (set.distance) totalDistance += set.distance;
        if (set.duration_seconds) totalDuration += set.duration_seconds;
        totalSets++;
      });
    });
    return { totalDistance, totalDuration, totalSets };
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-indigo-600 mb-4"></div>
        <div className="text-gray-600 font-medium">Loading your completed workout...</div>
      </div>
    );
  }

  // Error state
  if (error || !workout) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
        <span className="block sm:inline">{error || "Workout not found. It may have been deleted."}</span>
        <Link to="/" className="block mt-4 text-red-700 font-bold hover:underline">Return to Dashboard</Link>
      </div>
    );
  }

  // Calculate totals
  const strengthTotals = calculateStrengthTotals(workout.exercises || []);
  const cardioTotals = calculateCardioTotals(workout.exercises || []);

  return (
    <div className="max-w-4xl mx-auto">
      {/* Personal Record Celebration */}
      {showCelebration && currentRecord && (
        <PersonalRecordCelebration
          show={showCelebration}
          record={currentRecord}
          onClose={() => {
            if (window.celebrationClosedHandler) {
              window.celebrationClosedHandler();
            } else {
              setShowCelebration(false);
            }
          }}
        />
      )}

      <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
        <div className="flex items-center mb-4">
          <div className="bg-green-500 rounded-full p-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-green-800 ml-3">Workout Completed!</h2>

          {workoutScore && (
            <div className="ml-auto bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full flex items-center">
              <i className="fas fa-star text-yellow-500 mr-2"></i>
              <span className="font-bold text-xl">{workoutScore.total_score}</span>
              <span className="ml-1 text-sm">points</span>
            </div>
          )}
        </div>
        <p className="text-green-700 mb-4">
          Great job! You've completed your workout on {format(new Date(workout.date), 'MMMM d, yyyy')}.
        </p>

        {/* Workout Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-white rounded-lg shadow-sm p-4">
            <h3 className="font-semibold text-gray-700 mb-2">Exercises</h3>
            <p className="text-2xl font-bold text-indigo-600">{workout.exercises?.length || 0}</p>
          </div>
          {strengthTotals.totalSets > 0 && (
            <div className="bg-white rounded-lg shadow-sm p-4">
              <h3 className="font-semibold text-gray-700 mb-2">Total Weight</h3>
              <p className="text-2xl font-bold text-indigo-600">{strengthTotals.totalWeight} lbs</p>
            </div>
          )}
          {cardioTotals.totalSets > 0 && (
            <div className="bg-white rounded-lg shadow-sm p-4">
              <h3 className="font-semibold text-gray-700 mb-2">Cardio Distance</h3>
              <p className="text-2xl font-bold text-indigo-600">{cardioTotals.totalDistance.toFixed(2)} miles</p>
            </div>
          )}
          {strengthTotals.totalSets > 0 && (
            <div className="bg-white rounded-lg shadow-sm p-4">
              <h3 className="font-semibold text-gray-700 mb-2">Total Reps</h3>
              <p className="text-2xl font-bold text-indigo-600">{strengthTotals.totalReps}</p>
            </div>
          )}
          {cardioTotals.totalSets > 0 && (
            <div className="bg-white rounded-lg shadow-sm p-4">
              <h3 className="font-semibold text-gray-700 mb-2">Cardio Time</h3>
              <p className="text-2xl font-bold text-indigo-600">{formatDuration(cardioTotals.totalDuration)}</p>
            </div>
          )}
          <div className="bg-white rounded-lg shadow-sm p-4">
            <h3 className="font-semibold text-gray-700 mb-2">Total Sets</h3>
            <p className="text-2xl font-bold text-indigo-600">{strengthTotals.totalSets + cardioTotals.totalSets}</p>
          </div>
        </div>

        {/* Score Breakdown */}
        {workoutScore && (
          <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
            <h3 className="font-semibold text-gray-700 mb-3">Score Breakdown</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-sm text-gray-500">Base Score</div>
                <div className="text-lg font-bold text-indigo-600">{workoutScore.base_score}</div>
              </div>

              {workoutScore.streak_bonus > 0 && (
                <div className="bg-gray-50 p-3 rounded">
                  <div className="text-sm text-gray-500">Streak Bonus</div>
                  <div className="text-lg font-bold text-orange-500">+{workoutScore.streak_bonus}</div>
                  {workoutScore.details?.streak_days && (
                    <div className="text-xs text-gray-500">{workoutScore.details.streak_days} day streak</div>
                  )}
                </div>
              )}

              {workoutScore.variety_bonus > 0 && (
                <div className="bg-gray-50 p-3 rounded">
                  <div className="text-sm text-gray-500">Variety Bonus</div>
                  <div className="text-lg font-bold text-purple-500">+{workoutScore.variety_bonus}</div>
                  {workoutScore.details?.exercise_types && (
                    <div className="text-xs text-gray-500">{workoutScore.details.exercise_types.length} exercise types</div>
                  )}
                </div>
              )}

              {workoutScore.new_exercise_bonus > 0 && (
                <div className="bg-gray-50 p-3 rounded">
                  <div className="text-sm text-gray-500">New Exercise Bonus</div>
                  <div className="text-lg font-bold text-green-500">+{workoutScore.new_exercise_bonus}</div>
                  {workoutScore.details?.new_exercises && (
                    <div className="text-xs text-gray-500">{workoutScore.details.new_exercises.length} new exercises</div>
                  )}
                </div>
              )}

              <div className="bg-yellow-50 p-3 rounded sm:col-span-2 md:col-span-4">
                <div className="text-sm text-gray-500">Total Score</div>
                <div className="text-xl font-bold text-yellow-600">{workoutScore.total_score}</div>
                <div className="text-xs text-gray-500 mt-1">
                  <Link to="/progress" className="text-indigo-600 hover:underline">
                    View your progress and achievements
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex space-x-4">
          <Link to="/" className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
            Return to Dashboard
          </Link>
          <Link
            to={`/workout/${workout.id}`}
            className="bg-white hover:bg-gray-100 text-indigo-600 font-bold py-2 px-4 rounded border border-indigo-600"
          >
            View Workout Details
          </Link>
        </div>
      </div>

      {/* Workout Analysis Section */}
      <div className="bg-white border rounded-lg shadow p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-800">Workout Analysis</h2>

          {/* Only show button when not loading and no analysis */}
          {!analysisLoading && !analysis && (
            <button
              onClick={handleGenerateAnalysis}
              className="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded"
            >
              Generate Analysis
            </button>
          )}
        </div>

        {/* Display analysis loading state */}
        {analysisLoading && (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600 mb-4"></div>
            <p className="text-gray-600">Generating your workout analysis...</p>
            <p className="text-gray-500 text-sm mt-2">This may take a few moments</p>
          </div>
        )}

        {/* Display analysis content when available */}
        {!analysisLoading && analysis && (
          <div className="space-y-6">
            {/* Summary Section */}
            {analysis.summary && (
              <div>
                <h3 className="font-semibold text-lg text-gray-700 mb-2">Summary</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  {analysis.summary.split('\n').map((paragraph, index) => (
                    <p key={index} className="mb-2 text-gray-800">{paragraph}</p>
                  ))}
                </div>
              </div>
            )}

            {/* Progress Section */}
            {analysis.progress && (
              <div>
                <h3 className="font-semibold text-lg text-gray-700 mb-2">Progress</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  {analysis.progress.split('\n').map((paragraph, index) => (
                    <p key={index} className="mb-2 text-gray-800">{paragraph}</p>
                  ))}
                </div>
              </div>
            )}

            {/* Insights Section */}
            {analysis.insights && (
              <div>
                <h3 className="font-semibold text-lg text-gray-700 mb-2">Insights</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  {analysis.insights.split('\n').map((paragraph, index) => (
                    <p key={index} className="mb-2 text-gray-800">{paragraph}</p>
                  ))}
                </div>
              </div>
            )}

            {/* Recommendations Section */}
            {analysis.recommendations && (
              <div>
                <h3 className="font-semibold text-lg text-gray-700 mb-2">Recommendations</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  {analysis.recommendations.split('\n').map((paragraph, index) => (
                    <p key={index} className="mb-2 text-gray-800">{paragraph}</p>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* No analysis placeholder */}
        {!analysisLoading && !analysis && (
          <div className="bg-gray-50 p-6 rounded-lg text-center">
            <p className="text-gray-600 mb-3">No analysis has been generated for this workout yet.</p>
            <p className="text-gray-500">Click the "Generate Analysis" button to create an AI-powered analysis of your workout.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkoutComplete;