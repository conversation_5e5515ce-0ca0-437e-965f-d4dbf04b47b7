// frontend/src/pages/GenerateWorkout.jsx
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../services/api';

const GenerateWorkout = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [loadingEquipment, setLoadingEquipment] = useState(true);
  const [error, setError] = useState('');
  const [generatedWorkout, setGeneratedWorkout] = useState(null);

  // Form state
  const [selectedEquipment, setSelectedEquipment] = useState([]);
  const [exerciseCount, setExerciseCount] = useState(5);
  const [goals, setGoals] = useState('');
  const [equipmentList, setEquipmentList] = useState([]);

  // Load equipment list
  useEffect(() => {
    const fetchEquipment = async () => {
      try {
        setLoadingEquipment(true);
        const data = await api.getEquipment();
        setEquipmentList(data);
      } catch (err) {
        console.error('Error fetching equipment:', err);
        setError('Failed to load equipment list. Please refresh the page.');
      } finally {
        setLoadingEquipment(false);
      }
    };

    fetchEquipment();
  }, []);

  // Handle equipment selection
  const handleEquipmentChange = (e) => {
    const value = e.target.value;
    if (e.target.checked) {
      setSelectedEquipment([...selectedEquipment, value]);
    } else {
      setSelectedEquipment(selectedEquipment.filter(item => item !== value));
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Basic validation
    if (!goals.trim()) {
      setError('Please enter your workout goals');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const response = await api.generateWorkout({
        equipment: selectedEquipment,
        exercise_count: exerciseCount,
        goals: goals
      });

      setGeneratedWorkout(response);
      setLoading(false);
    } catch (err) {
      console.error('Error generating workout:', err);
      setError('Failed to generate workout. Please try again.');
      setLoading(false);
    }
  };

  // Create a workout from the generated exercises
  const handleCreateWorkout = async () => {
    try {
      setLoading(true);

      // Mark exercises as coming from LLM
      const llmExercises = generatedWorkout.exercises.map(ex => ({
        ...ex,
        from_llm: true // Flag to indicate this exercise was generated by LLM
      }));

      const workout = await api.createWorkoutFromGenerated({
        exercises: llmExercises,
        description: `Generated workout: ${goals}`
      });

      // Navigate to the active workout page
      navigate('/active-workout', {
        state: {
          workoutCreated: true,
          workoutId: workout.id
        }
      });
    } catch (err) {
      console.error('Error creating workout:', err);
      setError('Failed to create workout. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Generate Workout</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white shadow-md rounded-lg p-6 mb-6">
        <form onSubmit={handleSubmit}>
          {/* Equipment Selection */}
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Available Equipment
            </label>
            {loadingEquipment ? (
              <div className="animate-pulse h-10 bg-gray-200 rounded"></div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                {equipmentList.map((equipment) => (
                  <div key={equipment.id} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`equipment-${equipment.id}`}
                      value={equipment.name}
                      onChange={handleEquipmentChange}
                      className="mr-2"
                    />
                    <label htmlFor={`equipment-${equipment.id}`} className="text-gray-700">
                      {equipment.name}
                    </label>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Exercise Count */}
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Number of Exercises
            </label>
            <input
              type="number"
              min="1"
              max="10"
              value={exerciseCount}
              onChange={(e) => setExerciseCount(parseInt(e.target.value))}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            />
          </div>

          {/* Goals */}
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Your Workout Goals*
            </label>
            <textarea
              value={goals}
              onChange={(e) => setGoals(e.target.value)}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline h-32"
              placeholder="Describe what you want to achieve with this workout (e.g., 'I want to build bigger arms' or 'I need a full-body workout focusing on strength')"
              required
            ></textarea>
          </div>

          {/* Submit Button */}
          <div className="flex items-center justify-between">
            <button
              type="submit"
              disabled={loading}
              className={`
                ${loading ? 'bg-gray-500' : 'bg-indigo-600 hover:bg-indigo-700'}
                text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full
              `}
            >
              {loading ? 'Generating Workout...' : 'Generate Workout'}
            </button>
          </div>
        </form>
      </div>

      {/* Results Section */}
      {loading && (
        <div className="flex justify-center items-center py-10">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          <span className="ml-3 text-gray-600">Generating your personalized workout...</span>
        </div>
      )}

      {!loading && generatedWorkout && (
        <div className="bg-white shadow-md rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">Your Generated Workout</h2>
            <button
              onClick={handleCreateWorkout}
              className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
            >
              Begin Workout
            </button>
          </div>

          {/* General advice */}
          {generatedWorkout.generalAdvice && (
            <div className="bg-indigo-50 p-4 rounded-lg mb-6">
              <h3 className="font-semibold text-indigo-800 mb-2">Workout Advice</h3>
              <p className="text-indigo-700">{generatedWorkout.generalAdvice}</p>
            </div>
          )}

          {/* Exercise list */}
          <div className="space-y-4">
            {generatedWorkout.exercises?.map((exercise, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-bold text-lg">{exercise.name}</h3>
                    {exercise.type && (
                      <span className="inline-block bg-gray-200 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 mr-2 mb-2">
                        {exercise.type}
                      </span>
                    )}
                    {exercise.equipment && (
                      <span className="inline-block bg-gray-200 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 mr-2 mb-2">
                        {exercise.equipment}
                      </span>
                    )}
                  </div>
                </div>

                {exercise.description && (
                  <p className="text-gray-700 mt-2">{exercise.description}</p>
                )}

                {exercise.instructions && (
                  <div className="mt-2">
                    <h4 className="font-semibold text-gray-800">Instructions:</h4>
                    <ol className="list-decimal list-inside text-gray-700">
                      {exercise.instructions.map((step, i) => (
                        <li key={i} className="mt-1">{step}</li>
                      ))}
                    </ol>
                  </div>
                )}

                {exercise.sets && exercise.reps && (
                  <div className="mt-2 text-gray-700">
                    <strong>Recommended:</strong> {exercise.sets} sets of {exercise.reps} reps
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="mt-6 flex justify-center">
            <button
              onClick={handleCreateWorkout}
              className="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg text-lg"
            >
              Begin Workout
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default GenerateWorkout;
