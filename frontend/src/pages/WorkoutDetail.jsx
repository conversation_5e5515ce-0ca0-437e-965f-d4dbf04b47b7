// frontend/src/pages/WorkoutDetail.jsx
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { format } from 'date-fns';
import api from '../services/api';
import WorkoutAnalysis from '../components/WorkoutAnalysis';
import { formatDuration } from '../utils/timeUtils';

const WorkoutDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [workout, setWorkout] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [editDate, setEditDate] = useState('');
  const [editDescription, setEditDescription] = useState('');
  const [analysisRequested, setAnalysisRequested] = useState(false);

  const [isSocialWorkout, setIsSocialWorkout] = useState(false);

  useEffect(() => {
    const fetchWorkout = async () => {
      try {
        setLoading(true);
        // First try to fetch as a user's own workout
        try {
          const data = await api.getWorkout(id);
          setWorkout(data);
          setEditDate(format(new Date(data.date), 'yyyy-MM-dd'));
          setEditDescription(data.description || '');
          setIsSocialWorkout(false);
        } catch (err) {
          // If that fails, try to fetch as a social workout
          console.log('Not user\'s workout, trying social endpoint...');
          const socialData = await api.getSocialWorkout(id);
          setWorkout(socialData);
          setIsSocialWorkout(true);
        }
        setLoading(false);
      } catch (err) {
        console.error('Error fetching workout:', err);
        setError('Failed to load workout details. Please try again later.');
        setLoading(false);
      }
    };

    fetchWorkout();
  }, [id]);

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this workout?')) {
      try {
        await api.deleteWorkout(id);
        navigate('/');
      } catch (err) {
        console.error('Error deleting workout:', err);
        setError('Failed to delete workout. Please try again.');
      }
    }
  };

  const handleUpdate = async (e) => {
    e.preventDefault();
    setError('');

    try {
      const data = await api.updateWorkout(id, {
        date: new Date(editDate),
        description: editDescription,
        status: workout.status
      });
      setWorkout(data);
      setIsEditing(false);
    } catch (err) {
      console.error('Error updating workout:', err);
      setError('Failed to update workout. Please try again.');
    }
  };

  // Format duration is now imported from timeUtils

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-gray-600">Loading workout details...</div>
      </div>
    );
  }

  if (!workout && !loading) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
        <span className="block sm:inline">Workout not found. It may have been deleted.</span>
        <Link to="/" className="block mt-4 text-red-700 font-bold hover:underline">
          Return to Dashboard
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {isEditing ? (
        <div className="px-6 py-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">Edit Workout</h2>

          <form onSubmit={handleUpdate}>
            <div className="mb-4">
              <label htmlFor="date" className="block text-gray-700 text-sm font-bold mb-2">
                Workout Date
              </label>
              <input
                type="date"
                id="date"
                value={editDate}
                onChange={(e) => setEditDate(e.target.value)}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
              />
            </div>

            <div className="mb-6">
              <label htmlFor="description" className="block text-gray-700 text-sm font-bold mb-2">
                Workout Notes
              </label>
              <textarea
                id="description"
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline h-32"
              ></textarea>
            </div>

            <div className="flex items-center justify-between">
              <button
                type="button"
                onClick={() => setIsEditing(false)}
                className="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
              >
                Save Changes
              </button>
            </div>
          </form>
        </div>
      ) : (
        <div className="px-6 py-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800">
              {isSocialWorkout ? (
                <span>
                  {workout.username}'s Workout on {format(new Date(workout.date), 'MMMM d, yyyy')}
                </span>
              ) : (
                <span>
                  Workout on {format(new Date(workout.date), 'MMMM d, yyyy')}
                </span>
              )}
            </h2>
            {!isSocialWorkout && (
              <div className="space-x-2">
                <button
                  onClick={() => setIsEditing(true)}
                  className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                >
                  Edit
                </button>
                <button
                  onClick={handleDelete}
                  className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                >
                  Delete
                </button>
              </div>
            )}
          </div>

          {/* Status Badge */}
          <div className="mb-4">
            <span
              className={`px-3 py-1 rounded-full text-sm font-medium
              ${workout.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`}
            >
              {workout.status === 'in_progress' ? 'In Progress' : 'Completed'}
            </span>
          </div>

          {/* Exercises Section */}
          {workout.exercises && workout.exercises.length > 0 ? (
            <div className="mb-8">
              <h3 className="text-xl font-semibold text-gray-800 mb-4">Exercises</h3>

              <div className="space-y-6">
                {workout.exercises.map((exercise) => (
                  <div key={exercise.id} className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-lg mb-2">
                      {exercise.name} <span className="text-gray-500 text-sm capitalize">({exercise.exercise_type})</span>
                    </h4>

                    {exercise.sets && exercise.sets.length > 0 ? (
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-100">
                            <tr>
                              <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Set
                              </th>
                              {exercise.exercise_type === 'strength' && (
                                <>
                                  <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Reps
                                  </th>
                                  <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Weight (lbs)
                                  </th>
                                </>
                              )}
                              {exercise.exercise_type === 'cardio' && (
                                <>
                                  <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Duration
                                  </th>
                                  <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Distance (miles)
                                  </th>
                                </>
                              )}
                              <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Notes
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {exercise.sets.map((set, index) => (
                              <tr key={set.id}>
                                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{index + 1}</td>
                                {exercise.exercise_type === 'strength' && (
                                  <>
                                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{set.reps}</td>
                                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{set.weight}</td>
                                  </>
                                )}
                                {exercise.exercise_type === 'cardio' && (
                                  <>
                                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                                      {formatDuration(set.duration_seconds)}
                                    </td>
                                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{set.distance}</td>
                                  </>
                                )}
                                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{set.notes}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <p className="text-gray-500 italic">No sets recorded for this exercise.</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="bg-gray-50 p-4 rounded-lg mb-6">
              <p className="text-gray-500 italic">No exercises were recorded for this workout.</p>
            </div>
          )}

          {/* Workout Notes */}
          {workout.description && (
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-gray-800 mb-2">Workout Notes</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="whitespace-pre-line text-gray-800">
                  {workout.description}
                </p>
              </div>
            </div>
          )}

          <div className="mt-8">
            <Link
              to="/"
              className="text-indigo-600 hover:text-indigo-800 font-semibold"
            >
              &larr; Back to Dashboard
            </Link>
          </div>

          {/* Workout Analysis - only show for user's own workouts */}
          {workout.status === 'completed' && !isSocialWorkout && (
            <div className="mt-8">
              {analysisRequested ? (
                <WorkoutAnalysis workoutId={workout.id} />
              ) : (
                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                  <div className="bg-indigo-50 px-6 py-4 border-b border-indigo-100">
                    <h3 className="text-xl font-semibold text-indigo-800">Workout Analysis</h3>
                    <p className="text-indigo-600 text-sm">
                      Get AI-powered insights about your workout performance
                    </p>
                  </div>
                  <div className="p-6 text-center">
                    <button
                      onClick={() => setAnalysisRequested(true)}
                      className="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded"
                    >
                      Generate Analysis
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default WorkoutDetail;