// frontend/src/pages/ActiveWorkout.jsx
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { format } from 'date-fns';
import api from '../services/api';
import ExerciseSelector from '../components/ExerciseSelector';
import PersonalRecordCelebration from '../components/PersonalRecordCelebration';
import TimeInput from '../components/TimeInput';
import { formatDuration } from '../utils/timeUtils';

const ActiveWorkout = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const workoutCreated = location.state?.workoutCreated;
  const workoutId = location.state?.workoutId;

  // ──────────────────────────────────────────── state ───┐
  const [workout, setWorkout] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [completing, setCompleting] = useState(false);
  const [showExerciseSelector, setShowExerciseSelector] = useState(false);

  // Personal record celebration
  const [showCelebration, setShowCelebration] = useState(false);
  const [newRecord, setNewRecord] = useState(null);

  // Previous max weight
  const [previousMaxWeight, setPreviousMaxWeight] = useState(null);

  // form helpers -----------------------------------------------------------
  const [exerciseName, setExerciseName] = useState('');
  const [exerciseType, setExerciseType] = useState('strength');
  const [activeExercise, setActiveExercise] = useState(null);

  const [reps, setReps] = useState('');
  const [weight, setWeight] = useState('');
  const [duration, setDuration] = useState('');
  const [distance, setDistance] = useState('');
  const [notes, setNotes] = useState('');
  const [summaryNotes, setSummaryNotes] = useState('');
  // ─────────────────────────────────────────────────────────────────────────

  const debug = (msg, ...args) => console.log(`[ActiveWorkout] ${msg}`, ...args);

  // ① get (or create) the workout once ─────────────────────────────────────
  useEffect(() => {
    // If we are finishing a workout we *do not* want to fire another poll.
    if (completing) {
      debug('Skipping fetch while completing');
      return;
    }

    const fetchOrCreate = async () => {
      try {
        setLoading(true);
        setError('');

        // If we have a workoutId from the GenerateWorkout page, use that
        if (workoutCreated && workoutId) {
          debug('Loading workout from GenerateWorkout:', workoutId);
          const workout = await api.getWorkout(workoutId);
          setWorkout(workout);
          // Clear the location state to prevent reloading the same workout on refresh
          window.history.replaceState({}, document.title);
        } else {
          // Otherwise, get or create a workout as usual
          const existing = await api.getWorkoutInProgress();
          if (existing) {
            debug('Loaded in‑progress workout:', existing);
            setWorkout(existing);
          } else {
            debug('No in‑progress workout, starting a new one');
            const fresh = await api.startWorkout();
            setWorkout(fresh);
          }
        }
      } catch (err) {
        debug('Fetch/create error', err);
        setError('Failed to load or create workout. Please refresh.');
      } finally {
        setLoading(false);
      }
    };

    fetchOrCreate();
  }, [completing, workoutCreated, workoutId]); // rerun when these dependencies change

  // ② CRUD helpers (exercises / sets) ──────────────────────────────────────
  const handleAddExercise = async (e) => {
    e?.preventDefault();
    if (!exerciseName.trim()) return setError('Exercise name is required');
    try {
      setError('');
      const newEx = await api.addExercise(workout.id, {
        name: exerciseName,
        exercise_type: exerciseType,
        sets: []
      });
      setWorkout({
        ...workout,
        exercises: [...(workout.exercises ?? []), newEx]
      });
      setActiveExercise(newEx);
      setExerciseName('');

      // If it's a strength exercise, fetch the previous max weight
      if (exerciseType === 'strength') {
        const maxWeightData = await api.getExerciseMaxWeight(exerciseName);
        setPreviousMaxWeight(maxWeightData);
      } else {
        setPreviousMaxWeight(null);
      }
    } catch (err) {
      debug('addExercise error', err);
      setError('Failed to add exercise');
    }
  };

  const handleSelectExercise = (exercise) => {
    setExerciseName(exercise.name);
    setExerciseType(exercise.exercise_type);
    // Clear any existing errors
    setError('');
    // Automatically add the exercise after selection
    setTimeout(() => {
      if (!exercise.name.trim()) return;
      try {
        api.addExercise(workout.id, {
          name: exercise.name,
          exercise_type: exercise.exercise_type,
          sets: []
        }).then(newEx => {
          setWorkout({
            ...workout,
            exercises: [...(workout.exercises ?? []), newEx]
          });
          setActiveExercise(newEx);
          setExerciseName('');

          // If it's a strength exercise, fetch the previous max weight
          if (exercise.exercise_type === 'strength') {
            api.getExerciseMaxWeight(exercise.name).then(maxWeightData => {
              setPreviousMaxWeight(maxWeightData);
            });
          } else {
            setPreviousMaxWeight(null);
          }
        });
      } catch (err) {
        debug('addExercise error from selection', err);
        setError('Failed to add selected exercise');
      }
    }, 100);
  };

  const handleAddSet = async (e) => {
    e.preventDefault();
    if (!activeExercise) return setError('No exercise selected');

    // basic validation ----------------------------------------------------
    if (activeExercise.exercise_type === 'strength' && (!reps || !weight)) {
      return setError('Reps and weight are required');
    }
    if (activeExercise.exercise_type === 'cardio') {
      if (!duration) {
        return setError('Duration is required');
      }
      if (!distance) {
        return setError('Distance is required');
      }
    }

    try {
      setError('');
      const payload = {
        reps: reps ? +reps : null,
        weight: weight ? +weight : null,
        duration_seconds: duration ? +duration : null,
        distance: distance ? +distance : null,
        notes
      };
      const newSet = await api.addSet(activeExercise.id, payload);

      const updatedExercises = workout.exercises.map((ex) =>
        ex.id === activeExercise.id ? { ...ex, sets: [...ex.sets, newSet] } : ex
      );
      setWorkout({ ...workout, exercises: updatedExercises });
      setActiveExercise({ ...activeExercise, sets: [...activeExercise.sets, newSet] });

      // If this is a strength exercise, still update the displayed max weights
      // but don't show celebration
      if (activeExercise.exercise_type === 'strength' && previousMaxWeight) {
        const currentWeight = payload.weight;
        let updatedMaxWeight = {...previousMaxWeight};
        
        // Always update the previous max since this is the most recent workout
        updatedMaxWeight.previous_max = Math.max(currentWeight, previousMaxWeight.previous_max || 0);
        updatedMaxWeight.previous_max_date = new Date();
        updatedMaxWeight.has_history = true;

        setPreviousMaxWeight(updatedMaxWeight);
      }

      // reset form --------------------------------------------------------
      setReps('');
      setWeight('');
      setDuration('');
      setDistance('');
      setNotes('');
    } catch (err) {
      debug('addSet error', err);
      setError('Failed to add set');
    }
  };

  // Check if the current set is a personal record
  const checkForPersonalRecord = async (exercise, setData) => {
    try {
      debug('Checking for personal record');

      // Get all personal records
      const records = await api.getPersonalRecords();

      // Create a record object based on the exercise type
      let potentialRecord = null;

      if (exercise.exercise_type === 'strength' && setData.weight) {
        // For strength exercises, check if this is a max weight PR
        const existingRecord = records.find(r =>
          r.exercise_name === exercise.name &&
          r.exercise_type === 'strength'
        );

        // If no existing record or new weight is STRICTLY higher (not equal)
        if (!existingRecord || setData.weight > existingRecord.value) {
          // Double-check to make sure we're not celebrating the same value
          if (existingRecord && setData.weight === existingRecord.value) {
            debug('Weight matches existing record, not showing celebration');
          } else {
            potentialRecord = {
              exercise_name: exercise.name,
              exercise_type: 'strength',
              value: setData.weight,
              unit: 'lbs',
              workout_id: workout.id
            };
          }
        }
      } else if (exercise.exercise_type === 'cardio' && setData.distance) {
        // For cardio, check if this is a distance PR
        const existingDistanceRecord = records.find(r =>
          r.exercise_name === exercise.name &&
          r.exercise_type === 'cardio_distance'
        );

        // If no existing record or new distance is STRICTLY higher (not equal)
        if (!existingDistanceRecord || setData.distance > existingDistanceRecord.value) {
          // Double-check to make sure we're not celebrating the same value
          if (existingDistanceRecord && setData.distance === existingDistanceRecord.value) {
            debug('Distance matches existing record, not showing celebration');
          } else {
            potentialRecord = {
              exercise_name: exercise.name,
              exercise_type: 'cardio_distance',
              value: setData.distance,
              unit: 'miles',
              workout_id: workout.id
            };
          }
        }

        // Also check for pace PR if both distance and duration are provided
        if (setData.distance && setData.duration_seconds) {
          const pace = setData.duration_seconds / setData.distance; // seconds per mile
          const existingPaceRecord = records.find(r =>
            r.exercise_name === exercise.name &&
            r.exercise_type === 'cardio_pace'
          );

          // For pace, lower is better (STRICTLY lower, not equal)
          if (!existingPaceRecord || pace < existingPaceRecord.value) {
            // Double-check to make sure we're not celebrating the same value
            if (existingPaceRecord && pace === existingPaceRecord.value) {
              debug('Pace matches existing record, not showing celebration');
            } else {
              potentialRecord = {
                exercise_name: exercise.name,
                exercise_type: 'cardio_pace',
                value: pace,
                unit: 'sec/mile',
                workout_id: workout.id
              };
            }
          }
        }
      }

      // If we found a new record, show the celebration
      if (potentialRecord) {
        debug('New personal record!', potentialRecord);
        setNewRecord(potentialRecord);
        setShowCelebration(true);
      }
    } catch (err) {
      debug('Error checking for personal record:', err);
      // Non-fatal error, just log it
    }
  };

  const handleDeleteSet = async (setId) => {
    if (!window.confirm('Delete this set?')) return;
    try {
      await api.deleteSet(setId);
      const updEx = activeExercise.sets.filter((s) => s.id !== setId);
      const updatedExercises = workout.exercises.map((ex) =>
        ex.id === activeExercise.id ? { ...ex, sets: updEx } : ex
      );
      setWorkout({ ...workout, exercises: updatedExercises });
      setActiveExercise({ ...activeExercise, sets: updEx });
    } catch (err) {
      debug('deleteSet error', err);
      setError('Failed to delete set');
    }
  };

  const handleDeleteExercise = async (exerciseId) => {
    if (!window.confirm('Delete this exercise?')) return;
    try {
      await api.deleteExercise(exerciseId);
      const updated = workout.exercises.filter((ex) => ex.id !== exerciseId);
      setWorkout({ ...workout, exercises: updated });
      if (activeExercise?.id === exerciseId) setActiveExercise(null);
    } catch (err) {
      debug('deleteExercise error', err);
      setError('Failed to delete exercise');
    }
  };

  // ③ COMPLETE WORKOUT ────────────────────────────────────────────────────
  const handleCompleteWorkout = async () => {
    if (!window.confirm('Are you ready to complete this workout?')) return;
    try {
      setCompleting(true);  // guard other effects
      setError('');         // clear any existing errors

      debug(`Completing workout ${workout.id} with notes: ${summaryNotes}`);

      // Complete the workout with summary notes
      const completedWorkout = await api.completeWorkout(workout.id, summaryNotes);

      if (!completedWorkout) {
        throw new Error("Failed to complete workout - no response from server");
      }

      debug(`Workout completed successfully. Navigating to completion page.`);

      // Navigate to the completion page
      navigate(`/workout-complete/${workout.id}`);
    } catch (err) {
      debug('Error completing workout:', err);
      setError(`Failed to complete workout: ${err.message || 'Unknown error'}`);
      setCompleting(false);  // allow retry
    }
  };

  // ④ DISCARD WORKOUT ─────────────────────────────────────────────────────
  const handleDiscardWorkout = async () => {
    if (!window.confirm('Are you sure you want to discard this workout? This action cannot be undone.')) return;
    try {
      setCompleting(true);  // guard other effects
      setError('');         // clear any existing errors

      debug(`Discarding workout ${workout.id}`);

      // Discard the workout
      await api.discardWorkout(workout.id);

      debug(`Workout discarded successfully. Navigating to dashboard.`);

      // Navigate to the dashboard
      navigate('/');
    } catch (err) {
      debug('Error discarding workout:', err);
      setError(`Failed to discard workout: ${err.message || 'Unknown error'}`);
      setCompleting(false);  // allow retry
    }
  };

  // ─────────────────────────────────────── render helpers ────────────────
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <span className="text-gray-600">Loading workout…</span>
      </div>
    );
  }

  // Use the formatDuration utility function
  const fmtDuration = (sec) => formatDuration(sec);

  return (
    <div className="container mx-auto px-4" key={workout?.id}>
      {/* Personal Record Celebration */}
      {showCelebration && newRecord && (
        <PersonalRecordCelebration
          show={showCelebration}
          record={newRecord}
          onClose={() => setShowCelebration(false)}
        />
      )}

      {showExerciseSelector && (
        <ExerciseSelector
          onSelectExercise={handleSelectExercise}
          onClose={() => setShowExerciseSelector(false)}
        />
      )}
      <div className="bg-white shadow-md rounded-lg p-6 mb-6">
        {/* header ---------------------------------------------------------*/}
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold text-gray-800">
            Workout in Progress – {format(new Date(workout.date), 'MMM d, yyyy')}
          </h1>
          <div className="flex space-x-2">
            <button
              onClick={handleCompleteWorkout}
              disabled={completing}
              className="bg-green-600 hover:bg-green-700 disabled:opacity-50 text-white font-bold py-2 px-4 rounded"
            >
              {completing ? 'Completing…' : 'Complete Workout'}
            </button>
            <button
              onClick={handleDiscardWorkout}
              disabled={completing}
              className="bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white font-bold py-2 px-4 rounded"
            >
              Discard Workout
            </button>
          </div>
        </div>

        {/* error banner ----------------------------------------------------*/}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <div className="flex flex-col md:flex-row gap-6">
          {/* ▼ LEFT – Exercise list + add form */}
          <div className="md:w-1/3">
            <h2 className="text-xl font-semibold mb-3">Exercises</h2>

            {/* add exercise form */}
            <div className="bg-gray-50 p-4 rounded-lg mb-4">
              <button
                onClick={() => setShowExerciseSelector(true)}
                className="w-full btn btn-primary flex items-center justify-center"
              >
                <i className="fas fa-dumbbell mr-2"></i> Select Exercise
              </button>

              <div className="text-center mt-3">
                <span className="text-sm text-gray-500">or</span>
              </div>

              <form onSubmit={handleAddExercise} className="mt-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">Exercise Name</label>
                <input
                  value={exerciseName}
                  onChange={(e) => setExerciseName(e.target.value)}
                  className="w-full border-gray-300 rounded-md mb-3 p-2 border"
                  placeholder="Bench Press, Treadmill…"
                  required
                />
                <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                <select
                  value={exerciseType}
                  onChange={(e) => setExerciseType(e.target.value)}
                  className="w-full border-gray-300 rounded-md mb-3 p-2 border"
                >
                  <option value="strength">Strength</option>
                  <option value="cardio">Cardio</option>
                  <option value="flexibility">Flexibility</option>
                  <option value="other">Other</option>
                </select>
                <button className="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded">
                  Add Custom Exercise
                </button>
              </form>
            </div>

            {/* exercise list */}
            <div className="bg-gray-50 p-4 rounded-lg">
              {(!workout.exercises || !workout.exercises.length) && (
                <p className="text-gray-500 text-sm">No exercises yet.</p>
              )}
              <ul className="divide-y divide-gray-200">
                {workout.exercises?.map((ex) => (
                  <li
                    key={ex.id}
                    className={`py-3 flex justify-between items-center cursor-pointer ${
                      activeExercise?.id === ex.id ? 'bg-indigo-50' : ''
                    }`}
                    onClick={() => {
                      setActiveExercise(ex);
                      // Fetch max weight for strength exercises when selected
                      if (ex.exercise_type === 'strength') {
                        api.getExerciseMaxWeight(ex.name).then(maxWeightData => {
                          setPreviousMaxWeight(maxWeightData);
                        });
                      } else {
                        setPreviousMaxWeight(null);
                      }
                    }}
                  >
                    <div>
                      <h4 className="font-medium">{ex.name}</h4>
                      <p className="text-sm text-gray-500 capitalize">{ex.exercise_type}</p>
                      <p className="text-sm text-gray-500">{ex.sets.length} sets</p>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteExercise(ex.id);
                      }}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Delete
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* ▼ RIGHT – Active exercise */}
          <div className="md:w-2/3">
            {!activeExercise ? (
              <div className="bg-gray-50 p-8 rounded-lg text-center text-gray-500">
                Select an exercise to log sets.
              </div>
            ) : (
              <>
                <h2 className="text-xl font-semibold mb-3">
                  {activeExercise.name}{' '}
                  <span className="text-gray-500 text-sm capitalize">({activeExercise.exercise_type})</span>
                </h2>

                {/* Display previous and all-time max weights if available */}
                {activeExercise.exercise_type === 'strength' && previousMaxWeight && previousMaxWeight.has_history && (
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
                    <div className="flex flex-col space-y-2">
                      {previousMaxWeight.previous_max && (
                        <p className="text-blue-800">
                          <span className="font-bold">Last Workout Max:</span> {previousMaxWeight.previous_max} lbs
                          {previousMaxWeight.previous_max_date && (
                            <span className="text-sm text-blue-600 ml-2">
                              ({format(new Date(previousMaxWeight.previous_max_date), 'MMM d, yyyy')})
                            </span>
                          )}
                        </p>
                      )}

                      {previousMaxWeight.all_time_max && (
                        <p className="text-blue-800 font-medium">
                          <span className="font-bold">All-Time Max:</span> {previousMaxWeight.all_time_max} lbs
                          {previousMaxWeight.all_time_max_date && (
                            <span className="text-sm text-blue-600 ml-2">
                              ({format(new Date(previousMaxWeight.all_time_max_date), 'MMM d, yyyy')})
                            </span>
                          )}
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {/* add set */}
                <form onSubmit={handleAddSet} className="bg-gray-50 p-4 rounded-lg mb-4">
                  <h3 className="text-lg font-medium mb-3">Add Set</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {activeExercise.exercise_type === 'strength' && (
                      <>
                        <input
                          type="number"
                          className="border border-gray-300 rounded-md p-2"
                          placeholder="Reps"
                          value={reps}
                          onChange={(e) => setReps(e.target.value)}
                          min="1"
                        />
                        <input
                          type="number"
                          className="border border-gray-300 rounded-md p-2"
                          placeholder="Weight (lbs)"
                          value={weight}
                          onChange={(e) => setWeight(e.target.value)}
                          min="0"
                          step="0.5"
                        />
                      </>
                    )}

                    {activeExercise.exercise_type === 'cardio' && (
                      <>
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-1">Duration</label>
                          <TimeInput
                            value={duration}
                            onChange={(value) => setDuration(value)}
                            placeholder="Duration"
                          />
                        </div>
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-1">Distance (miles)</label>
                          <input
                            type="number"
                            className="w-full border border-gray-300 rounded-md p-2"
                            placeholder="Distance (mi)"
                            value={distance}
                            onChange={(e) => setDistance(e.target.value)}
                            min="0"
                            step="0.01"
                          />
                        </div>
                      </>
                    )}

                    <textarea
                      className="md:col-span-2 border border-gray-300 rounded-md p-2"
                      placeholder="Notes (optional)"
                      rows="2"
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                    />
                  </div>

                  <button className="mt-4 w-full bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded">
                    Add Set
                  </button>
                </form>

                {/* sets table */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="text-lg font-medium mb-3">Sets</h3>
                  {!activeExercise.sets.length ? (
                    <p className="text-gray-500 text-sm">No sets logged.</p>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 text-sm">
                        <thead className="bg-gray-100">
                          <tr>
                            <th className="px-6 py-3 text-left font-medium text-gray-500 uppercase tracking-wider">#</th>
                            {activeExercise.exercise_type === 'strength' && (
                              <>
                                <th className="px-6 py-3 text-left font-medium text-gray-500 uppercase tracking-wider">Reps</th>
                                <th className="px-6 py-3 text-left font-medium text-gray-500 uppercase tracking-wider">Weight</th>
                              </>
                            )}
                            {activeExercise.exercise_type === 'cardio' && (
                              <>
                                <th className="px-6 py-3 text-left font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                                <th className="px-6 py-3 text-left font-medium text-gray-500 uppercase tracking-wider">Distance</th>
                              </>
                            )}
                            <th className="px-6 py-3 text-left font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                            <th className="px-6 py-3 text-left font-medium text-gray-500 uppercase tracking-wider">Action</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {activeExercise.sets.map((set, idx) => (
                            <tr key={set.id}>
                              <td className="px-6 py-4 whitespace-nowrap">{idx + 1}</td>
                              {activeExercise.exercise_type === 'strength' && (
                                <>
                                  <td className="px-6 py-4 whitespace-nowrap">{set.reps}</td>
                                  <td className="px-6 py-4 whitespace-nowrap">{set.weight}</td>
                                </>
                              )}
                              {activeExercise.exercise_type === 'cardio' && (
                                <>
                                  <td className="px-6 py-4 whitespace-nowrap">{set.duration_seconds ? fmtDuration(set.duration_seconds) : ''}</td>
                                  <td className="px-6 py-4 whitespace-nowrap">{set.distance}</td>
                                </>
                              )}
                              <td className="px-6 py-4 whitespace-nowrap">{set.notes}</td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <button
                                  onClick={() => handleDeleteSet(set.id)}
                                  className="text-red-600 hover:text-red-800"
                                >
                                  Delete
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              </>
            )}
          </div>
        </div>

        {/* summary notes */}
        <div className="mt-6">
          <h2 className="text-xl font-semibold mb-3">Workout Summary Notes</h2>
          <textarea
            value={summaryNotes}
            onChange={(e) => setSummaryNotes(e.target.value)}
            rows="3"
            className="w-full border border-gray-300 rounded-md p-2"
            placeholder="How did it go today?"
          />
        </div>
      </div>
    </div>
  );
};

export default ActiveWorkout;
