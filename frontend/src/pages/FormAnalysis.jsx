// frontend/src/pages/FormAnalysis.jsx
import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import Webcam from 'react-webcam';
import api from '../services/api';
import CameraPositionGuide from '../components/CameraPositionGuide';

const FormAnalysis = () => {
  const navigate = useNavigate();
  const webcamRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const [capturing, setCapturing] = useState(false);
  const [recordedChunks, setRecordedChunks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [exercises, setExercises] = useState([]);
  const [selectedExercise, setSelectedExercise] = useState(null);
  const [formAnalysis, setFormAnalysis] = useState(null);
  const [cameraPermission, setCameraPermission] = useState(null);
  const [showInstructions, setShowInstructions] = useState(true);
  const [showCameraGuide, setShowCameraGuide] = useState(true);

  // Fetch exercises on component mount
  useEffect(() => {
    const fetchExercises = async () => {
      try {
        const data = await api.getExerciseLibrary();

        // For now, only show exercises that we've implemented form analysis for
        // Currently, only "Barbell Back Squat" is supported
        const supportedExercises = data.filter(ex =>
          ex.name.toLowerCase().includes('squat') &&
          ex.name.toLowerCase().includes('back') &&
          ex.exercise_type === 'strength'
        );

        if (supportedExercises.length === 0) {
          // If no supported exercises are found, show a message
          setError('No supported exercises found. Currently, only Barbell Back Squat is supported for form analysis.');
        } else {
          setExercises(supportedExercises);
        }
      } catch (err) {
        console.error('Error fetching exercises:', err);
        setError('Failed to load exercises. Please try again.');
      }
    };

    fetchExercises();

    // Check for camera permission on desktop browsers
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      navigator.mediaDevices.getUserMedia({ video: true })
        .then(() => setCameraPermission(true))
        .catch(() => setCameraPermission(false));
    } else {
      // For devices without mediaDevices API (like some mobile browsers)
      setCameraPermission(null);
    }

    // Cleanup function
    return () => {
      if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
        mediaRecorderRef.current.stop();
      }
    };
  }, []);

  const handleStartCapture = () => {
    setCapturing(true);
    setRecordedChunks([]);

    // Create a new form analysis session
    if (selectedExercise) {
      setLoading(true);
      api.createFormAnalysis(selectedExercise.id)
        .then(response => {
          setFormAnalysis(response);

          // Start recording
          mediaRecorderRef.current = new MediaRecorder(webcamRef.current.stream, {
            mimeType: 'video/webm'
          });

          mediaRecorderRef.current.addEventListener('dataavailable', handleDataAvailable);
          mediaRecorderRef.current.addEventListener('stop', handleStopCapture);
          mediaRecorderRef.current.start();

          // Automatically stop recording after 10 seconds
          setTimeout(() => {
            if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
              mediaRecorderRef.current.stop();
            }
          }, 10000);
        })
        .catch(err => {
          console.error('Error creating form analysis:', err);
          setError('Failed to create form analysis session. Please try again.');
          setCapturing(false);
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      setError('Please select an exercise first.');
      setCapturing(false);
    }
  };

  const handleDataAvailable = ({ data }) => {
    if (data.size > 0) {
      setRecordedChunks(prev => [...prev, data]);
    }
  };

  const handleStopCapture = () => {
    setCapturing(false);

    // Process the recorded video
    if (recordedChunks.length > 0 && formAnalysis) {
      setLoading(true);

      // Create a blob from the recorded chunks
      const blob = new Blob(recordedChunks, {
        type: 'video/webm'
      });

      // Create a FormData object to send the video
      const formData = new FormData();
      formData.append('video', blob);

      // Upload the video for analysis
      api.uploadFormVideo(formAnalysis.id, formData)
        .then(response => {
          setFormAnalysis(response);
        })
        .catch(err => {
          console.error('Error uploading video:', err);
          setError('Failed to upload video for analysis. Please try again.');
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  const handleSelectExercise = (exercise) => {
    setSelectedExercise(exercise);
    setFormAnalysis(null);
    setRecordedChunks([]);
  };

  // Handle file upload for mobile devices
  const fileInputRef = useRef(null);
  const [videoFile, setVideoFile] = useState(null);

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file && file.type.startsWith('video/')) {
      setVideoFile(file);
      setError('');
    } else if (file) {
      setError('Please select a valid video file.');
    }
  };

  const handleFileUpload = async () => {
    if (!videoFile) {
      setError('Please select a video file first.');
      return;
    }

    if (!selectedExercise) {
      setError('Please select an exercise first.');
      return;
    }

    setLoading(true);
    setError(''); // Clear any previous errors

    try {
      console.log('Starting form analysis for exercise:', selectedExercise.name);

      // Create a form analysis session
      console.log('Creating form analysis session...');
      const analysisSession = await api.createFormAnalysis(selectedExercise.id);
      console.log('Form analysis session created:', analysisSession);
      setFormAnalysis(analysisSession);

      // Create a FormData object to send the video
      console.log('Preparing video for upload...');
      const formData = new FormData();
      formData.append('video', videoFile);

      // Log file details for debugging
      const fileDetails = {
        name: videoFile.name,
        type: videoFile.type,
        size: videoFile.size,
        lastModified: new Date(videoFile.lastModified).toISOString()
      };
      console.log('File details:', fileDetails);

      // Upload the video for analysis
      console.log('Uploading video for analysis...');
      const response = await api.uploadFormVideo(analysisSession.id, formData);
      console.log('Analysis complete:', response);
      setFormAnalysis(response);
    } catch (err) {
      console.error('Error uploading video:', err);

      // More detailed error message
      let errorMessage = 'Failed to upload and analyze video. ';

      if (err.message) {
        errorMessage += `Error: ${err.message}`;
      }

      if (err.response) {
        errorMessage += ` Status: ${err.response.status}`;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const renderCameraView = () => {
    // Detect iOS devices
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

    // If we're on iOS, show file upload option instead of webcam
    if (isIOS) {
      return (
        <div className="p-4">
          <div className="bg-yellow-50 border-l-4 border-yellow-500 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">iOS Device Detected</h3>
                <p className="text-sm text-yellow-700 mt-1">
                  Direct camera access is not available on iOS devices through the browser. Please record a video using your camera app and upload it here.
                </p>
                <ol className="list-decimal ml-5 mt-2 text-sm text-yellow-700">
                  <li>Open your Camera app</li>
                  <li>Record a video of your squat from the side view</li>
                  <li>Upload the video using the button below</li>
                </ol>
              </div>
            </div>
          </div>

          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <input
              type="file"
              accept="video/*"
              onChange={handleFileChange}
              ref={fileInputRef}
              className="hidden"
            />

            {videoFile ? (
              <div>
                <p className="text-gray-700 mb-4">Selected file: <span className="font-medium">{videoFile.name}</span></p>
                <video
                  className="mx-auto rounded-lg shadow-md mb-4 max-h-80"
                  controls
                  src={URL.createObjectURL(videoFile)}
                />
                <button
                  onClick={() => setVideoFile(null)}
                  className="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg mr-2"
                >
                  Remove
                </button>
                <button
                  onClick={handleFileUpload}
                  disabled={loading || !selectedExercise}
                  className="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg"
                >
                  {loading ? 'Analyzing...' : 'Analyze Form'}
                </button>
              </div>
            ) : (
              <div>
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <p className="mt-2 text-gray-600">Drag and drop a video file, or</p>
                <button
                  onClick={() => fileInputRef.current.click()}
                  className="mt-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg"
                >
                  Select Video
                </button>
              </div>
            )}
          </div>
        </div>
      );
    }

    // For non-iOS devices, check camera permission
    if (cameraPermission === false) {
      return (
        <div className="p-4">
          <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Camera Access Required</h3>
                <p className="text-sm text-red-700 mt-1">
                  Camera access is required for form analysis. Please allow camera access in your browser settings and refresh the page.
                </p>
                <div className="mt-3">
                  <p className="text-sm text-red-700 font-medium">Alternative: Upload a video</p>
                  <input
                    type="file"
                    accept="video/*"
                    onChange={handleFileChange}
                    ref={fileInputRef}
                    className="mt-2"
                  />
                  {videoFile && (
                    <div className="mt-3">
                      <button
                        onClick={handleFileUpload}
                        disabled={loading || !selectedExercise}
                        className="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg"
                      >
                        {loading ? 'Analyzing...' : 'Analyze Form'}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // For devices with camera access
    return (
      <div className="relative">
        <Webcam
          audio={false}
          ref={webcamRef}
          screenshotFormat="image/jpeg"
          videoConstraints={{
            width: 640,
            height: 480,
            facingMode: "user"
          }}
          className="rounded-lg shadow-md w-full"
        />

        {capturing && (
          <div className="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full animate-pulse">
            Recording
          </div>
        )}

        <div className="mt-4 flex justify-center">
          {!capturing ? (
            <button
              onClick={handleStartCapture}
              disabled={!selectedExercise || loading}
              className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-lg disabled:opacity-50"
            >
              {loading ? 'Processing...' : 'Start Recording'}
            </button>
          ) : (
            <button
              onClick={() => {
                if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
                  mediaRecorderRef.current.stop();
                }
              }}
              className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg"
            >
              Stop Recording
            </button>
          )}
        </div>

        <div className="mt-4 text-center text-gray-600 text-sm">
          <p>Or upload a pre-recorded video:</p>
          <input
            type="file"
            accept="video/*"
            onChange={handleFileChange}
            ref={fileInputRef}
            className="mt-2"
          />
          {videoFile && (
            <div className="mt-3">
              <button
                onClick={handleFileUpload}
                disabled={loading || !selectedExercise}
                className="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg"
              >
                {loading ? 'Analyzing...' : 'Analyze Uploaded Video'}
              </button>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderAnalysisResults = () => {
    if (!formAnalysis || !formAnalysis.overall_score) {
      return null;
    }

    // Determine score color
    const getScoreColor = (score) => {
      if (score >= 8) return 'text-green-600';
      if (score >= 6) return 'text-yellow-600';
      return 'text-red-600';
    };

    return (
      <div className="bg-white rounded-lg shadow-md p-6 mt-6">
        <h3 className="text-xl font-semibold text-gray-800 mb-4">Form Analysis Results</h3>

        <div className="flex items-center mb-6">
          <div className={`text-4xl font-bold ${getScoreColor(formAnalysis.overall_score)}`}>
            {formAnalysis.overall_score.toFixed(1)}/10
          </div>
          <div className="ml-4 text-gray-600">
            Overall Form Score
          </div>
        </div>

        <div className="mb-6">
          <h4 className="text-lg font-medium text-gray-800 mb-2">Feedback</h4>
          <p className="text-gray-600 whitespace-pre-line">{formAnalysis.feedback}</p>
        </div>

        {formAnalysis.issues && formAnalysis.issues.length > 0 && (
          <div className="mb-6">
            <h4 className="text-lg font-medium text-gray-800 mb-2">Detected Issues</h4>
            <ul className="space-y-2">
              {formAnalysis.issues.map((issue, index) => (
                <li key={index} className="bg-red-50 border-l-4 border-red-500 p-3 rounded">
                  <div className="font-medium text-red-800">{issue.description}</div>
                  {issue.recommendation && (
                    <div className="text-red-700 mt-1">{issue.recommendation}</div>
                  )}
                </li>
              ))}
            </ul>
          </div>
        )}

        <div>
          <h4 className="text-lg font-medium text-gray-800 mb-2">Recommendations</h4>
          <p className="text-gray-600 whitespace-pre-line">{formAnalysis.recommendations}</p>
        </div>
      </div>
    );
  };

  const renderInstructions = () => {
    if (!showInstructions) return null;

    return (
      <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6 rounded-md">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium text-blue-800">Instructions for Barbell Back Squat Form Analysis</h3>

            <div className="mt-2 text-sm text-blue-700">
              <h4 className="font-medium mb-1">Camera Setup:</h4>
              <ul className="list-disc pl-5 space-y-1 mb-3">
                <li>Position your camera 6-8 feet away from your workout area</li>
                <li>Place the camera at hip height (approximately 3 feet from the ground)</li>
                <li>For barbell back squats, a side view (90° angle) is best for analyzing knee, hip, and back angles</li>
                <li>Ensure your entire body is visible in the frame, from head to feet</li>
                <li>If possible, use a tripod or stable surface to prevent camera movement</li>
                <li>Make sure the area behind you has a plain background with good contrast to your body</li>
              </ul>

              <h4 className="font-medium mb-1">Lighting Tips:</h4>
              <ul className="list-disc pl-5 space-y-1 mb-3">
                <li>Use even, diffused lighting from the front or sides</li>
                <li>Avoid backlighting (light sources behind you) which creates silhouettes</li>
                <li>Avoid harsh shadows that might obscure joint positions</li>
                <li>Natural daylight or multiple light sources work best</li>
              </ul>

              <h4 className="font-medium mb-1">Clothing & Performance:</h4>
              <ul className="list-disc pl-5 space-y-1">
                <li>Wear form-fitting athletic clothing in a color that contrasts with your background</li>
                <li>Avoid loose clothing that might hide joint positions</li>
                <li>Perform 2-3 complete squat repetitions during the recording</li>
                <li>Move at a controlled pace (not too fast) for best analysis results</li>
                <li>Start in the standing position, then perform full squats with proper form</li>
              </ul>
            </div>
          </div>
          <button
            onClick={() => setShowInstructions(false)}
            className="ml-2 text-blue-500 hover:text-blue-700"
          >
            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">Exercise Form Analysis</h1>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {renderInstructions()}

      {showCameraGuide && (
        <div className="relative">
          <CameraPositionGuide />
          <button
            onClick={() => setShowCameraGuide(false)}
            className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
            aria-label="Close camera guide"
          >
            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Select Exercise</h2>

            <div className="space-y-2">
              {exercises.map(exercise => (
                <div
                  key={exercise.id}
                  className={`p-3 rounded-lg cursor-pointer transition-colors ${
                    selectedExercise?.id === exercise.id
                      ? 'bg-indigo-100 border-indigo-300 border'
                      : 'hover:bg-gray-100 border border-gray-200'
                  }`}
                  onClick={() => handleSelectExercise(exercise)}
                >
                  <div className="font-medium">{exercise.name}</div>
                  {exercise.description && (
                    <div className="text-sm text-gray-600 mt-1">{exercise.description}</div>
                  )}
                </div>
              ))}

              {exercises.length === 0 && !loading && (
                <div className="text-gray-500 text-center py-4">
                  No exercises found
                </div>
              )}

              {loading && exercises.length === 0 && (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500 mx-auto"></div>
                  <div className="mt-2 text-gray-600">Loading exercises...</div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="md:col-span-2">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-800">
                {selectedExercise ? `Analyzing: ${selectedExercise.name}` : 'Camera View'}
              </h2>

              {!showCameraGuide && (
                <button
                  onClick={() => setShowCameraGuide(true)}
                  className="text-sm text-indigo-600 hover:text-indigo-800 flex items-center"
                >
                  <svg className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  Show Camera Guide
                </button>
              )}
            </div>

            {renderCameraView()}
          </div>

          {renderAnalysisResults()}
        </div>
      </div>
    </div>
  );
};

export default FormAnalysis;
