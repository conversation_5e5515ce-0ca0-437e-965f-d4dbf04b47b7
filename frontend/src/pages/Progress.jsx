// frontend/src/pages/Progress.jsx
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import PullToRefresh from '../components/PullToRefresh';

const Progress = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState(null);
  const [milestones, setMilestones] = useState([]);
  const [achievements, setAchievements] = useState([]);
  const [allAchievements, setAllAchievements] = useState([]);
  const { token, logout } = useAuth();
  const navigate = useNavigate();

  // Debug utility
  const debugLog = (message, ...args) => {
    console.log(`[Progress] ${message}`, ...args);
  };

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch user stats
      const userStats = await api.getUserStats();
      setStats(userStats);
      debugLog('User stats:', userStats);

      // Fetch user milestones
      const userMilestones = await api.getUserMilestones();
      setMilestones(userMilestones);
      debugLog('User milestones:', userMilestones);

      // Fetch user achievements
      const userAchievements = await api.getUserAchievements();
      setAchievements(userAchievements);
      debugLog('User achievements:', userAchievements);

      // Fetch all achievements
      const achievements = await api.getAllAchievements();
      setAllAchievements(achievements);
      debugLog('All achievements:', achievements);

    } catch (err) {
      debugLog('Error fetching data:', err);
      setError(err.message || 'Failed to fetch progress data');

      // Handle authentication errors
      if (err.status === 401 || err.message?.includes('Not authenticated')) {
        logout();
        navigate('/login', {
          state: {
            error: 'Your session has expired. Please log in again.'
          }
        });
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (token) {
      fetchData();
    }
  }, [token]);

  const handleRefresh = async () => {
    debugLog('Pull-to-refresh triggered');
    await fetchData();
  };

  // Calculate achievement progress
  const calculateAchievementProgress = () => {
    if (!allAchievements.length) return 0;
    return Math.round((achievements.length / allAchievements.length) * 100);
  };

  // Format milestone status for display
  const formatMilestoneStatus = (milestone) => {
    const percentage = Math.min(100, Math.round((milestone.current_value / milestone.target_value) * 100));
    return {
      percentage,
      statusText: milestone.status === 'completed'
        ? 'Completed'
        : `${milestone.current_value} / ${milestone.target_value}`
    };
  };

  // Get icon for milestone type
  const getMilestoneIcon = (type) => {
    switch (type) {
      case 'weekly_workouts': return 'fa-calendar-week';
      case 'monthly_workouts': return 'fa-calendar-alt';
      case 'yearly_workouts': return 'fa-calendar';
      case 'consecutive_days': return 'fa-fire';
      case 'exercise_variety': return 'fa-dumbbell';
      default: return 'fa-trophy';
    }
  };

  // Get icon for achievement
  const getAchievementIcon = (icon) => {
    return icon || 'fa-award';
  };

  return (
    <PullToRefresh onRefresh={handleRefresh}>
      <div className="mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-dark-800 mb-4">
          Your Progress
        </h1>
        <p className="text-gray-600">
          Track your fitness journey, achievements, and milestones.
        </p>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="spinner"></div>
        </div>
      ) : error ? (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
          <p>{error}</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* User Stats Card */}
          <div className="lg:col-span-1">
            <div className="card p-6">
              <h2 className="text-xl font-bold mb-4 text-dark-800">
                <i className="fas fa-chart-line mr-2"></i> Your Stats
              </h2>

              {stats && (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Total Score</span>
                    <span className="text-xl font-bold text-primary-600">{stats.total_score}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Current Streak</span>
                    <span className="font-semibold">
                      <i className="fas fa-fire text-orange-500 mr-1"></i> {stats.current_streak} days
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Longest Streak</span>
                    <span className="font-semibold">
                      <i className="fas fa-trophy text-yellow-500 mr-1"></i> {stats.longest_streak} days
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Total Workouts</span>
                    <span className="font-semibold">{stats.total_workouts}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Unique Exercises</span>
                    <span className="font-semibold">{stats.unique_exercises}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Achievements</span>
                    <span className="font-semibold">{achievements.length} / {allAchievements.length}</span>
                  </div>

                  {/* Achievement Progress Bar */}
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div
                        className="bg-primary-600 h-2.5 rounded-full"
                        style={{ width: `${calculateAchievementProgress()}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1 text-right">
                      {calculateAchievementProgress()}% complete
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Milestones Section */}
          <div className="lg:col-span-2">
            <div className="card p-6">
              <h2 className="text-xl font-bold mb-4 text-dark-800">
                <i className="fas fa-flag-checkered mr-2"></i> Milestones
              </h2>

              {milestones.length === 0 ? (
                <p className="text-gray-500 italic">No milestones available yet.</p>
              ) : (
                <div className="space-y-4">
                  {milestones.map((milestone) => {
                    const { percentage, statusText } = formatMilestoneStatus(milestone);
                    return (
                      <div key={milestone.id} className="border-b border-gray-100 pb-4 last:border-0 last:pb-0">
                        <div className="flex items-center mb-2">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                            milestone.status === 'completed' ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'
                          }`}>
                            <i className={`fas ${getMilestoneIcon(milestone.milestone_type)}`}></i>
                          </div>
                          <div>
                            <h3 className="font-semibold text-dark-800">{milestone.title}</h3>
                            <p className="text-sm text-gray-600">{milestone.description}</p>
                          </div>
                          {milestone.status === 'completed' && (
                            <div className="ml-auto bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                              +{milestone.points_awarded} pts
                            </div>
                          )}
                        </div>

                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className={`h-2.5 rounded-full ${
                              milestone.status === 'completed' ? 'bg-green-500' : 'bg-blue-500'
                            }`}
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        <div className="flex justify-between mt-1">
                          <span className="text-xs text-gray-500">{statusText}</span>
                          <span className="text-xs text-gray-500">{percentage}%</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>

          {/* Achievements Section */}
          <div className="lg:col-span-3">
            <div className="card p-6">
              <h2 className="text-xl font-bold mb-4 text-dark-800">
                <i className="fas fa-award mr-2"></i> Achievements
              </h2>

              {allAchievements.length === 0 ? (
                <p className="text-gray-500 italic">No achievements available yet.</p>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {allAchievements.map((achievement) => {
                    const earned = achievements.some(a => a.achievement_id === achievement.id);
                    const userAchievement = achievements.find(a => a.achievement_id === achievement.id);

                    return (
                      <div
                        key={achievement.id}
                        className={`border rounded-lg p-4 flex flex-col items-center text-center ${
                          earned ? 'bg-yellow-50 border-yellow-200' : 'bg-gray-50 border-gray-200 opacity-70'
                        }`}
                      >
                        <div className={`w-16 h-16 rounded-full flex items-center justify-center mb-3 ${
                          earned ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-200 text-gray-400'
                        }`}>
                          <i className={`fas ${getAchievementIcon(achievement.icon)} text-2xl`}></i>
                        </div>
                        <h3 className="font-semibold text-dark-800">{achievement.name}</h3>
                        <p className="text-sm text-gray-600 mb-2">{achievement.description}</p>
                        {earned ? (
                          <div className="mt-auto">
                            <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                              Earned +{achievement.points} pts
                            </span>
                            {userAchievement && (
                              <p className="text-xs text-gray-500 mt-1">
                                {new Date(userAchievement.date_earned).toLocaleDateString()}
                              </p>
                            )}
                          </div>
                        ) : (
                          <div className="mt-auto">
                            <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                              Locked
                            </span>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </PullToRefresh>
  );
};

export default Progress;
