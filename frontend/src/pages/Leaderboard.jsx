// frontend/src/pages/Leaderboard.jsx
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import PullToRefresh from '../components/PullToRefresh';

const Leaderboard = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [leaderboard, setLeaderboard] = useState([]);
  const { token, user, logout } = useAuth();
  const navigate = useNavigate();

  // Debug utility
  const debugLog = (message, ...args) => {
    console.log(`[Leaderboard] ${message}`, ...args);
  };

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch leaderboard data
      const leaderboardData = await api.getLeaderboard();
      setLeaderboard(leaderboardData);
      debugLog('Leaderboard data:', leaderboardData);

    } catch (err) {
      debugLog('Error fetching data:', err);
      setError(err.message || 'Failed to fetch leaderboard data');

      // Handle authentication errors
      if (err.status === 401 || err.message?.includes('Not authenticated')) {
        logout();
        navigate('/login', {
          state: {
            error: 'Your session has expired. Please log in again.'
          }
        });
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (token) {
      fetchData();
    }
  }, [token]);

  const handleRefresh = async () => {
    debugLog('Pull-to-refresh triggered');
    await fetchData();
  };

  // Get user rank
  const getUserRank = () => {
    if (!user || !leaderboard.length) return null;
    const userIndex = leaderboard.findIndex(entry => entry.username === user.username);
    return userIndex !== -1 ? userIndex + 1 : null;
  };

  // Format rank with medal or number
  const formatRank = (index) => {
    const rank = index + 1;
    if (rank === 1) return <i className="fas fa-medal text-yellow-500"></i>;
    if (rank === 2) return <i className="fas fa-medal text-gray-400"></i>;
    if (rank === 3) return <i className="fas fa-medal text-amber-600"></i>;
    return rank;
  };

  return (
    <PullToRefresh onRefresh={handleRefresh}>
      <div className="mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-dark-800 mb-4">
          Leaderboard
        </h1>
        <p className="text-gray-600">
          See how you stack up against other users.
        </p>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="spinner"></div>
        </div>
      ) : error ? (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
          <p>{error}</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6">
          {/* User's Rank Card */}
          {user && getUserRank() && (
            <div className="card p-6 bg-primary-50 border border-primary-100">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                  <span className="text-primary-600 font-bold">#{getUserRank()}</span>
                </div>
                <div>
                  <h2 className="text-xl font-bold text-dark-800">Your Ranking</h2>
                  <p className="text-gray-600">
                    You're ranked #{getUserRank()} out of {leaderboard.length} users
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Leaderboard Table */}
          <div className="card overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rank
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Score
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Workouts
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Streak
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Achievements
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {leaderboard.length === 0 ? (
                    <tr>
                      <td colSpan="6" className="px-6 py-4 text-center text-gray-500 italic">
                        No leaderboard data available yet.
                      </td>
                    </tr>
                  ) : (
                    leaderboard.map((entry, index) => (
                      <tr
                        key={entry.username}
                        className={`${user && entry.username === user.username ? 'bg-primary-50' : ''} hover:bg-gray-50`}
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center
                              ${index === 0 ? 'bg-yellow-100' : index === 1 ? 'bg-gray-100' : index === 2 ? 'bg-amber-100' : ''}`}
                            >
                              <span className={`font-bold ${
                                index === 0 ? 'text-yellow-600' :
                                index === 1 ? 'text-gray-600' :
                                index === 2 ? 'text-amber-600' :
                                'text-gray-500'
                              }`}>
                                {formatRank(index)}
                              </span>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {entry.username}
                                {user && entry.username === user.username && (
                                  <span className="ml-2 text-xs bg-primary-100 text-primary-600 px-2 py-0.5 rounded-full">
                                    You
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 font-bold">{entry.total_score}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{entry.total_workouts}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <i className="fas fa-fire text-orange-500 mr-1"></i>
                            <span className="text-sm text-gray-900">{entry.current_streak}</span>
                            <span className="text-xs text-gray-500 ml-1">
                              (max: {entry.longest_streak})
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            <i className="fas fa-award text-yellow-500 mr-1"></i>
                            {entry.achievement_count}
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Leaderboard Info */}
          <div className="card p-6">
            <h2 className="text-xl font-bold mb-4 text-dark-800">
              <i className="fas fa-info-circle mr-2"></i> How Scoring Works
            </h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-dark-800 mb-2">Base Points</h3>
                <ul className="list-disc list-inside text-gray-600 space-y-1">
                  <li>3 exercises = 70 points</li>
                  <li>4 exercises = 100 points</li>
                  <li>5 exercises = 130 points</li>
                  <li>Each additional exercise = +20 points</li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold text-dark-800 mb-2">Bonus Points</h3>
                <ul className="list-disc list-inside text-gray-600 space-y-1">
                  <li>Streak bonus: +10 points per consecutive day (max 50)</li>
                  <li>Variety bonus: +15 points for each different exercise type beyond the first</li>
                  <li>New exercise bonus: +20 points for each exercise never done before</li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold text-dark-800 mb-2">Achievement & Milestone Points</h3>
                <p className="text-gray-600">
                  Earn additional points by completing achievements and reaching milestones in your fitness journey.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </PullToRefresh>
  );
};

export default Leaderboard;