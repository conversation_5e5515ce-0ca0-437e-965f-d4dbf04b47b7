// frontend/src/pages/WeightTracking.jsx
import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';
import WeightEntryForm from '../components/WeightEntryForm';
import WeightHistoryChart from '../components/WeightHistoryChart';
import WeightHistoryTable from '../components/WeightHistoryTable';
import PullToRefresh from '../components/PullToRefresh';

const WeightTracking = () => {
  const [weightEntries, setWeightEntries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [editEntry, setEditEntry] = useState(null);
  const { token, logout } = useAuth();
  const navigate = useNavigate();

  // Debug logging helper
  const debugLog = (message, data) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[WeightTracking] ${message}`, data || '');
    }
  };

  // Fetch weight entries
  const fetchWeightEntries = useCallback(async () => {
    try {
      setLoading(true);
      setError('');
      
      const entries = await api.getWeightEntries();
      debugLog('Fetched weight entries:', entries);
      setWeightEntries(entries);
      
    } catch (err) {
      debugLog('Error fetching weight entries:', err);
      
      // Handle authentication errors
      if (err.status === 401 || err.message?.includes('Not authenticated')) {
        logout();
        navigate('/login', {
          state: {
            error: 'Your session has expired. Please log in again.'
          }
        });
      } else {
        setError('Failed to load weight entries. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  }, [token, logout, navigate]);

  // Initial data fetch
  useEffect(() => {
    if (token) {
      fetchWeightEntries();
    }
  }, [token, fetchWeightEntries]);

  // Handle refresh when pull-to-refresh is triggered
  const handleRefresh = useCallback(async () => {
    debugLog('Pull-to-refresh triggered');
    await fetchWeightEntries();
  }, [fetchWeightEntries]);

  // Handle weight entry added or updated
  const handleWeightAdded = useCallback((entry) => {
    debugLog('Weight entry added/updated:', entry);
    fetchWeightEntries();
  }, [fetchWeightEntries]);

  // Handle weight entry deleted
  const handleEntryDeleted = useCallback((id) => {
    debugLog('Weight entry deleted:', id);
    setWeightEntries(prev => prev.filter(entry => entry.id !== id));
  }, []);

  // Handle edit entry
  const handleEditEntry = useCallback((entry) => {
    debugLog('Editing entry:', entry);
    setEditEntry(entry);
    
    // Scroll to the form
    const formElement = document.getElementById('weight-entry-form');
    if (formElement) {
      formElement.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  // Handle cancel edit
  const handleCancelEdit = useCallback(() => {
    debugLog('Canceling edit');
    setEditEntry(null);
  }, []);

  return (
    <PullToRefresh onRefresh={handleRefresh}>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Weight Tracking</h1>
        
        {error && (
          <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded mb-6">
            <p>{error}</p>
          </div>
        )}
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Weight Entry Form */}
          <div className="lg:col-span-1" id="weight-entry-form">
            <WeightEntryForm 
              onWeightAdded={handleWeightAdded} 
              editEntry={editEntry}
              onCancelEdit={handleCancelEdit}
            />
          </div>
          
          {/* Weight History Chart */}
          <div className="lg:col-span-2">
            <WeightHistoryChart weightEntries={weightEntries} />
          </div>
          
          {/* Weight History Table */}
          <div className="lg:col-span-3 mt-6">
            <WeightHistoryTable 
              weightEntries={weightEntries} 
              onEntryDeleted={handleEntryDeleted}
              onEditEntry={handleEditEntry}
            />
          </div>
        </div>
      </div>
    </PullToRefresh>
  );
};

export default WeightTracking;
