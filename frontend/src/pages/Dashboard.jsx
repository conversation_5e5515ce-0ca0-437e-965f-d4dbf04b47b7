// frontend/src/pages/Dashboard.jsx
import React, { useState, useEffect, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';
import PersonalRecords from '../components/PersonalRecords';
import SocialFeed from '../components/SocialFeed';
import PullToRefresh from '../components/PullToRefresh';
import WorkoutGroup from '../components/WorkoutGroup';
import WorkoutFilter from '../components/WorkoutFilter';

const Dashboard = () => {
  const [workouts, setWorkouts] = useState([]);
  const [filteredWorkouts, setFilteredWorkouts] = useState([]);
  const [activeWorkout, setActiveWorkout] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedWorkouts, setSelectedWorkouts] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [filters, setFilters] = useState({
    exerciseType: '',
    dateRange: '',
    searchTerm: ''
  });
  const { user, logout, token } = useAuth();
  const navigate = useNavigate();

  // Debug logging function
  const debugLog = (message, ...args) => {
    console.log(`[Dashboard Debug] ${message}`, ...args);
  };

  const fetchData = useCallback(async () => {
    try {
      debugLog('Fetch data started');
      debugLog('Current token:', token);

      setLoading(true);
      setError(null);

      // Check for an active workout
      try {
        const inProgressWorkout = await api.getWorkoutInProgress();
        setActiveWorkout(inProgressWorkout);
      } catch (err) {
        debugLog('Error fetching active workout:', err);
        // Don't fail completely if just the active workout check fails
      }

      // Fetch all workouts
      const fetchedWorkouts = await api.getWorkouts();

      debugLog('Fetched workouts:', fetchedWorkouts);
      setWorkouts(fetchedWorkouts);
    } catch (err) {
      debugLog('Error fetching data:', err);

      // More detailed error handling
      if (err.status === 401 || err.message?.includes('Not authenticated')) {
        debugLog('Authentication error detected');
        logout();
        navigate('/login', {
          state: {
            error: 'Your session has expired. Please log in again.'
          }
        });
      } else {
        setError(err.message || 'Failed to fetch workouts');
      }
    } finally {
      setLoading(false);
    }
  }, [token, logout, navigate]);

  // Handle refresh when pull-to-refresh is triggered
  const handleRefresh = useCallback(async () => {
    debugLog('Pull-to-refresh triggered');
    await fetchData();
  }, [fetchData]);

  useEffect(() => {
    // Only fetch if we have a token
    if (token) {
      fetchData();
    } else {
      debugLog('No token found, redirecting to login');
      navigate('/login');
    }
  }, [token, fetchData, navigate]);

  // Function to handle checkbox changes
  const handleSelectWorkout = (workoutId) => {
    if (selectedWorkouts.includes(workoutId)) {
      setSelectedWorkouts(selectedWorkouts.filter(id => id !== workoutId));
    } else {
      setSelectedWorkouts([...selectedWorkouts, workoutId]);
    }
  };

  // Function to handle "select all" checkbox
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedWorkouts([]);
    } else {
      const completedWorkoutIds = filteredWorkouts
        .map(workout => workout.id);
      setSelectedWorkouts(completedWorkoutIds);
    }
    setSelectAll(!selectAll);
  };

  // Function to handle batch delete
  const handleBatchDelete = async () => {
    if (selectedWorkouts.length === 0) return;

    if (window.confirm(`Delete ${selectedWorkouts.length} selected workouts?`)) {
      try {
        setError(null);
        // Delete workouts one by one
        for (const id of selectedWorkouts) {
          await api.deleteWorkout(id);
        }

        // Refresh workout list
        const fetchedWorkouts = await api.getWorkouts();
        setWorkouts(fetchedWorkouts);
        setSelectedWorkouts([]);
        setSelectAll(false);
      } catch (err) {
        console.error('Error deleting workouts:', err);
        setError('Failed to delete selected workouts');
      }
    }
  };

  // Function to get exercise summary
  const getExerciseSummary = (workout) => {
    if (!workout.exercises || workout.exercises.length === 0) {
      return 'No exercises recorded';
    }

    const exerciseCount = workout.exercises.length;
    const setCount = workout.exercises.reduce((total, ex) => total + (ex.sets?.length || 0), 0);

    let summary = `${exerciseCount} exercise${exerciseCount > 1 ? 's' : ''}`;
    if (setCount > 0) {
      summary += `, ${setCount} set${setCount > 1 ? 's' : ''}`;
    }

    return summary;
  };

  // Function to handle filter changes
  const handleFilterChange = useCallback((newFilters) => {
    setFilters(newFilters);

    // Apply filters to workouts
    let result = [...workouts];

    // Filter by exercise type
    if (newFilters.exerciseType) {
      result = result.filter(workout =>
        workout.exercises.some(exercise =>
          exercise.exercise_type === newFilters.exerciseType
        )
      );
    }

    // Filter by date range
    if (newFilters.dateRange) {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      let startDate;
      switch (newFilters.dateRange) {
        case 'week':
          startDate = new Date(today);
          startDate.setDate(today.getDate() - today.getDay()); // Start of current week (Sunday)
          break;
        case 'month':
          startDate = new Date(today.getFullYear(), today.getMonth(), 1);
          break;
        case '3months':
          startDate = new Date(today);
          startDate.setMonth(today.getMonth() - 3);
          break;
        case 'year':
          startDate = new Date(today.getFullYear(), 0, 1);
          break;
        default:
          startDate = null;
      }

      if (startDate) {
        result = result.filter(workout => new Date(workout.date) >= startDate);
      }
    }

    // Filter by search term
    if (newFilters.searchTerm) {
      const searchLower = newFilters.searchTerm.toLowerCase();
      result = result.filter(workout =>
        // Search in workout description
        (workout.description && workout.description.toLowerCase().includes(searchLower)) ||
        // Search in exercise names
        workout.exercises.some(exercise =>
          exercise.name.toLowerCase().includes(searchLower)
        )
      );
    }

    setFilteredWorkouts(result);
  }, [workouts]);

  // Update filtered workouts when workouts change
  useEffect(() => {
    handleFilterChange(filters);
  }, [workouts, filters, handleFilterChange]);

  // Function to group workouts by time period
  const groupWorkoutsByTimePeriod = (workouts) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const thisWeekStart = new Date(today);
    thisWeekStart.setDate(today.getDate() - today.getDay()); // Start of current week (Sunday)

    const lastWeekStart = new Date(thisWeekStart);
    lastWeekStart.setDate(thisWeekStart.getDate() - 7); // Start of last week

    const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);

    const lastMonthStart = new Date(thisMonthStart);
    lastMonthStart.setMonth(thisMonthStart.getMonth() - 1);

    // Filter completed workouts
    const completedWorkouts = workouts.filter(workout =>
      workout.status === 'completed' || !workout.status
    );

    // Group workouts by time period
    return {
      today: completedWorkouts.filter(workout =>
        new Date(workout.date) >= today
      ),
      yesterday: completedWorkouts.filter(workout =>
        new Date(workout.date) >= yesterday && new Date(workout.date) < today
      ),
      thisWeek: completedWorkouts.filter(workout =>
        new Date(workout.date) >= thisWeekStart && new Date(workout.date) < yesterday
      ),
      lastWeek: completedWorkouts.filter(workout =>
        new Date(workout.date) >= lastWeekStart && new Date(workout.date) < thisWeekStart
      ),
      thisMonth: completedWorkouts.filter(workout =>
        new Date(workout.date) >= thisMonthStart && new Date(workout.date) < lastWeekStart
      ),
      lastMonth: completedWorkouts.filter(workout =>
        new Date(workout.date) >= lastMonthStart && new Date(workout.date) < thisMonthStart
      ),
      older: completedWorkouts.filter(workout =>
        new Date(workout.date) < lastMonthStart
      )
    };
  };

  const handleStartWorkout = async () => {
    try {
      debugLog('Starting new workout');
      setLoading(true);

      // Navigate to the active workout page
      navigate('/active-workout');
    } catch (err) {
      debugLog('Error starting workout:', err);
      setError('Failed to start workout');
      setLoading(false);
    }
  };

  const handleContinueWorkout = () => {
    navigate('/active-workout');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-primary-600"></div>
          <p className="mt-4 text-primary-600 font-medium">Loading your workouts...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md" role="alert">
        <div className="flex">
          <div className="flex-shrink-0">
            <i className="fas fa-exclamation-circle text-red-500"></i>
          </div>
          <div className="ml-3">
            <p className="font-medium">Error</p>
            <p className="text-sm">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <PullToRefresh onRefresh={handleRefresh}>
      {/* Page Header */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h1 className="text-2xl sm:text-3xl font-bold text-dark-800 mb-4 sm:mb-0">
            {activeWorkout ? 'Active Workout' : 'Your Workouts'}
          </h1>

          <div className="flex flex-wrap gap-3">
            {!activeWorkout && (
              <>
                <button
                  onClick={handleStartWorkout}
                  className="btn btn-primary"
                >
                  <i className="fas fa-plus-circle mr-2"></i> New Workout
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Active Workout Banner */}
      {activeWorkout && (
        <div className="card p-5 mb-8 border-l-4 border-primary-500">
          <div className="flex flex-col md:flex-row md:justify-between md:items-center">
            <div className="mb-4 md:mb-0">
              <div className="flex items-center mb-2">
                <span className="h-3 w-3 bg-green-500 rounded-full mr-2 animate-pulse"></span>
                <h2 className="text-xl font-semibold text-primary-700">Active Workout in Progress</h2>
              </div>
              <p className="text-dark-600">Started on {format(new Date(activeWorkout.date), 'MMMM d, yyyy')}</p>
              <p className="text-dark-600 mt-1">{getExerciseSummary(activeWorkout)}</p>
            </div>
            <button
              onClick={handleContinueWorkout}
              className="btn btn-primary"
            >
              <i className="fas fa-play-circle mr-2"></i> Continue Workout
            </button>
          </div>
        </div>
      )}

      {/* Dashboard Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content - Workouts List */}
        <div className="lg:col-span-2">
          {workouts.length === 0 ? (
            <div className="card p-10 text-center">
              <div className="flex justify-center mb-4">
                <div className="bg-primary-100 p-4 rounded-full">
                  <i className="fas fa-dumbbell text-4xl text-primary-500"></i>
                </div>
              </div>
              <h3 className="text-xl font-semibold text-dark-800 mb-2">No workouts yet</h3>
              <p className="text-dark-600 mb-6">
                Start tracking your fitness journey by logging your first workout.
              </p>
              <button
                onClick={handleStartWorkout}
                className="btn btn-primary"
              >
                <i className="fas fa-plus-circle mr-2"></i> Log Your First Workout
              </button>
            </div>
          ) : (
            <>
              {/* Filter Controls */}
              <WorkoutFilter onFilterChange={handleFilterChange} />

              {/* Selection Controls */}
              {filteredWorkouts.length > 0 && (
                <div className="flex flex-wrap justify-between items-center mb-4 bg-white p-3 rounded-lg shadow-sm">
                  <div className="flex items-center">
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectAll}
                        onChange={handleSelectAll}
                        className="form-checkbox mr-2"
                      />
                      <span className="text-sm text-dark-600">Select All</span>
                    </label>
                  </div>

                  <div className="flex items-center">
                    <span className="text-sm text-gray-500 mr-3">
                      {filteredWorkouts.length} workout{filteredWorkouts.length !== 1 ? 's' : ''}
                    </span>

                    {selectedWorkouts.length > 0 && (
                      <button
                        onClick={handleBatchDelete}
                        className="flex items-center text-red-600 hover:text-red-800 text-sm font-medium"
                      >
                        <i className="fas fa-trash-alt mr-1"></i> Delete Selected ({selectedWorkouts.length})
                      </button>
                    )}
                  </div>
                </div>
              )}

              {/* Grouped Workouts */}
              <div className="space-y-2">
                {filteredWorkouts.length === 0 ? (
                  <div className="bg-gray-50 p-6 rounded-lg text-center">
                    <div className="flex justify-center mb-4">
                      <div className="bg-gray-100 p-3 rounded-full">
                        <i className="fas fa-filter text-2xl text-gray-400"></i>
                      </div>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-700 mb-2">No workouts match your filters</h3>
                    <p className="text-gray-500 mb-4">
                      Try adjusting your filter criteria to see more workouts.
                    </p>
                  </div>
                ) : (
                  /* Group workouts by time period */
                  (() => {
                    const groupedWorkouts = groupWorkoutsByTimePeriod(filteredWorkouts);

                    return (
                      <>
                        <WorkoutGroup
                        title="Today"
                        workouts={groupedWorkouts.today}
                        getExerciseSummary={getExerciseSummary}
                        selectedWorkouts={selectedWorkouts}
                        onSelectWorkout={handleSelectWorkout}
                        expanded={true}
                      />

                      <WorkoutGroup
                        title="Yesterday"
                        workouts={groupedWorkouts.yesterday}
                        getExerciseSummary={getExerciseSummary}
                        selectedWorkouts={selectedWorkouts}
                        onSelectWorkout={handleSelectWorkout}
                        expanded={true}
                      />

                      <WorkoutGroup
                        title="This Week"
                        workouts={groupedWorkouts.thisWeek}
                        getExerciseSummary={getExerciseSummary}
                        selectedWorkouts={selectedWorkouts}
                        onSelectWorkout={handleSelectWorkout}
                        expanded={true}
                      />

                      <WorkoutGroup
                        title="Last Week"
                        workouts={groupedWorkouts.lastWeek}
                        getExerciseSummary={getExerciseSummary}
                        selectedWorkouts={selectedWorkouts}
                        onSelectWorkout={handleSelectWorkout}
                      />

                      <WorkoutGroup
                        title="This Month"
                        workouts={groupedWorkouts.thisMonth}
                        getExerciseSummary={getExerciseSummary}
                        selectedWorkouts={selectedWorkouts}
                        onSelectWorkout={handleSelectWorkout}
                      />

                      <WorkoutGroup
                        title="Last Month"
                        workouts={groupedWorkouts.lastMonth}
                        getExerciseSummary={getExerciseSummary}
                        selectedWorkouts={selectedWorkouts}
                        onSelectWorkout={handleSelectWorkout}
                      />

                      <WorkoutGroup
                        title="Older"
                        workouts={groupedWorkouts.older}
                        getExerciseSummary={getExerciseSummary}
                        selectedWorkouts={selectedWorkouts}
                        onSelectWorkout={handleSelectWorkout}
                      />
                    </>
                  );
                })()
                )}
              </div>
            </>
          )}
        </div>

        {/* Sidebar - Social Feed and Personal Records */}
        <div className="lg:col-span-1 space-y-6">
          {/* Social Feed */}
          <SocialFeed />

          {/* Personal Records */}
          <PersonalRecords />
        </div>
      </div>
    </PullToRefresh>
  );
};

export default Dashboard;