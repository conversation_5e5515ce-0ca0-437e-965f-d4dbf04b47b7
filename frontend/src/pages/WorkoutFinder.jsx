// frontend/src/pages/WorkoutFinder.jsx
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../services/api';

const WorkoutFinder = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [workoutSuggestions, setWorkoutSuggestions] = useState(null);
  
  // Form state
  const [selectedMuscle, setSelectedMuscle] = useState('');
  const [equipment, setEquipment] = useState([]);
  const [goals, setGoals] = useState('');

  // Available muscle groups
  const muscleGroups = [
    { value: 'chest', label: 'Chest' },
    { value: 'back', label: 'Back' },
    { value: 'shoulders', label: 'Shoulders' },
    { value: 'biceps', label: 'Biceps' },
    { value: 'triceps', label: 'Triceps' },
    { value: 'forearms', label: 'Forearms' },
    { value: 'quadriceps', label: 'Quadriceps' },
    { value: 'hamstrings', label: 'Hamstrings' },
    { value: 'calves', label: 'Calves' },
    { value: 'glutes', label: 'Glutes' },
    { value: 'abs', label: 'Abdominals' },
    { value: 'core', label: 'Core' },
    { value: 'full_body', label: 'Full Body' },
  ];

  // Available equipment
  const equipmentOptions = [
    { value: 'none', label: 'None (Bodyweight)' },
    { value: 'dumbbells', label: 'Dumbbells' },
    { value: 'barbell', label: 'Barbell' },
    { value: 'kettlebell', label: 'Kettlebell' },
    { value: 'resistance_bands', label: 'Resistance Bands' },
    { value: 'machines', label: 'Gym Machines' },
    { value: 'bench', label: 'Bench' },
    { value: 'pull_up_bar', label: 'Pull-up Bar' },
    { value: 'stability_ball', label: 'Stability Ball' },
  ];

  // Toggle equipment selection
  const toggleEquipment = (value) => {
    if (equipment.includes(value)) {
      setEquipment(equipment.filter(item => item !== value));
    } else {
      setEquipment([...equipment, value]);
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Basic validation
    if (!selectedMuscle) {
      setError('Please select a muscle group');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      const response = await api.getWorkoutSuggestions({
        muscleGroup: selectedMuscle,
        equipment: equipment,
        goals: goals
      });
      
      setWorkoutSuggestions(response);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching workout suggestions:', err);
      setError('Failed to get workout suggestions. Please try again.');
      setLoading(false);
    }
  };

  // Add a workout to the tracker
  const addToWorkout = (exercise) => {
    // Navigate to add workout page with pre-filled data
    navigate('/active-workout', { 
      state: { 
        suggestedExercise: {
          name: exercise.name,
          type: exercise.type || 'strength'
        }
      } 
    });
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Workout Finder</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white shadow-md rounded-lg p-6 mb-6">
        <form onSubmit={handleSubmit}>
          {/* Muscle Group Selection */}
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Target Muscle Group*
            </label>
            <select
              value={selectedMuscle}
              onChange={(e) => setSelectedMuscle(e.target.value)}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              required
            >
              <option value="">Select a muscle group</option>
              {muscleGroups.map((muscle) => (
                <option key={muscle.value} value={muscle.value}>
                  {muscle.label}
                </option>
              ))}
            </select>
          </div>

          {/* Equipment Selection */}
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Available Equipment (Optional)
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {equipmentOptions.map((item) => (
                <div key={item.value} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`equipment-${item.value}`}
                    checked={equipment.includes(item.value)}
                    onChange={() => toggleEquipment(item.value)}
                    className="mr-2"
                  />
                  <label htmlFor={`equipment-${item.value}`} className="text-sm text-gray-700">
                    {item.label}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Goals Input */}
          <div className="mb-6">
            <label htmlFor="goals" className="block text-gray-700 text-sm font-bold mb-2">
              Your Goals/Concerns (Optional)
            </label>
            <textarea
              id="goals"
              value={goals}
              onChange={(e) => setGoals(e.target.value)}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              rows="3"
              placeholder="E.g., Build strength, improve definition, work around a shoulder injury, etc."
            />
          </div>

          {/* Submit Button */}
          <div className="flex items-center justify-between">
            <button
              type="submit"
              disabled={loading}
              className={`
                ${loading ? 'bg-gray-500' : 'bg-indigo-600 hover:bg-indigo-700'}
                text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full
              `}
            >
              {loading ? 'Finding Workouts...' : 'Find Exercises'}
            </button>
          </div>
        </form>
      </div>

      {/* Results Section */}
      {loading && (
        <div className="flex justify-center items-center py-10">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          <span className="ml-3 text-gray-600">Generating workout suggestions...</span>
        </div>
      )}

      {!loading && workoutSuggestions && (
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-xl font-bold mb-4">Recommended Exercises</h2>
          
          {/* General advice */}
          {workoutSuggestions.generalAdvice && (
            <div className="bg-indigo-50 p-4 rounded-lg mb-6">
              <h3 className="font-semibold text-indigo-800 mb-2">Workout Advice</h3>
              <p className="text-indigo-700">{workoutSuggestions.generalAdvice}</p>
            </div>
          )}
          
          {/* Exercise list */}
          <div className="space-y-4">
            {workoutSuggestions.exercises?.map((exercise, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-bold text-lg">{exercise.name}</h3>
                    {exercise.type && (
                      <span className="inline-block bg-gray-200 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 mr-2 mb-2">
                        {exercise.type}
                      </span>
                    )}
                    {exercise.equipment && (
                      <span className="inline-block bg-gray-200 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 mr-2 mb-2">
                        {exercise.equipment}
                      </span>
                    )}
                  </div>
                  <button
                    onClick={() => addToWorkout(exercise)}
                    className="bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-bold py-1 px-3 rounded"
                  >
                    Add to Workout
                  </button>
                </div>
                
                {exercise.description && (
                  <p className="text-gray-700 mt-2">{exercise.description}</p>
                )}
                
                {exercise.instructions && (
                  <div className="mt-2">
                    <h4 className="font-semibold text-gray-800">Instructions:</h4>
                    <ol className="list-decimal list-inside text-gray-700">
                      {exercise.instructions.map((step, i) => (
                        <li key={i} className="mt-1">{step}</li>
                      ))}
                    </ol>
                  </div>
                )}
                
                {exercise.sets && exercise.reps && (
                  <div className="mt-2 text-gray-700">
                    <strong>Recommended:</strong> {exercise.sets} sets of {exercise.reps} reps
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default WorkoutFinder;