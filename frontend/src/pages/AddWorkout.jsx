import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';

const AddWorkout = () => {
  const [date, setDate] = useState(new Date());
  const [description, setDescription] = useState('');
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { user, logout } = useAuth();

  // Debug logging function
  const debugLog = (message, ...args) => {
    console.log(`[AddWorkout Debug] ${message}`, ...args);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate inputs
    if (!description.trim()) {
      setError('Description cannot be empty');
      return;
    }

    try {
      debugLog('Submitting workout:', { date, description });
      setLoading(true);
      setError(null);

      // Use the imported api service
      const newWorkout = await api.addWorkout({ 
        date, 
        description 
      });
      
      debugLog('Workout added successfully:', newWorkout);
      
      // Redirect to dashboard or show success message
      navigate('/');
    } catch (err) {
      debugLog('Error adding workout:', err);
      
      // Handle specific error types
      if (err.status === 401) {
        // Authentication error
        logout();
        navigate('/login', { 
          state: { 
            error: 'Your session has expired. Please log in again.' 
          } 
        });
      } else {
        // Other errors
        setError(err.message || 'Failed to add workout');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 max-w-md">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">Add New Workout</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
        <div className="mb-4">
          <label htmlFor="date" className="block text-gray-700 text-sm font-bold mb-2">
            Date
          </label>
          <input
            type="date"
            id="date"
            value={date.toISOString().split('T')[0]}
            onChange={(e) => setDate(new Date(e.target.value))}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            required
          />
        </div>

        <div className="mb-6">
          <label htmlFor="description" className="block text-gray-700 text-sm font-bold mb-2">
            Workout Description
          </label>
          <textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            placeholder="Describe your workout (e.g., 5k run, weight training)"
            required
            rows="4"
          />
        </div>

        <div className="flex items-center justify-between">
          <button
            type="submit"
            disabled={loading}
            className={`
              ${loading ? 'bg-gray-500' : 'bg-indigo-600 hover:bg-indigo-700'}
              text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline
            `}
          >
            {loading ? 'Adding...' : 'Add Workout'}
          </button>
          
          <button
            type="button"
            onClick={() => navigate('/')}
            className="text-gray-600 hover:text-gray-800"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddWorkout;