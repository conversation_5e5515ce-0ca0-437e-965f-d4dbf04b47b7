// frontend/src/services/api.js
const API_SERVICE = {
  baseUrl: '',
  debug: true,

  log(msg, data) {
    if (this.debug) console.log(`[API] ${msg}`, data || '');
  },

  async getWorkoutSuggestions(data) {
    try {
      this.log(`Requesting workout suggestions for ${data.muscleGroup}`);
      return this.fetchWithAuth(`${this.baseUrl}/workouts/suggestions/`, {
        method: 'POST',
        body: JSON.stringify(data),
      });
    } catch (err) {
      this.log(`Workout suggestions error: ${err.message}`);
      throw err;
    }
  },

  async generateWorkout(data) {
    try {
      this.log(`Generating workout with ${data.exercise_count} exercises`);
      return this.fetchWithAuth(`${this.baseUrl}/workouts/generate/`, {
        method: 'POST',
        body: JSON.stringify(data),
      });
    } catch (err) {
      this.log(`Workout generation error: ${err.message}`);
      throw err;
    }
  },

  async createWorkoutFromGenerated(data) {
    try {
      this.log(`Creating workout from ${data.exercises.length} generated exercises`);
      return this.fetchWithAuth(`${this.baseUrl}/workouts/create-from-generated/`, {
        method: 'POST',
        body: JSON.stringify(data),
      });
    } catch (err) {
      this.log(`Create workout from generated error: ${err.message}`);
      throw err;
    }
  },

  async fetchWithAuth(url, options = {}) {
    try {
      // Get token from localStorage directly
      const token = localStorage.getItem('token');
      this.log(`${options.method || 'GET'} ${url}`);
      
      const headers = {
        'Content-Type': 'application/json',
        Authorization: token ? `Bearer ${token}` : '',
        ...options.headers,
      };
      
      const res = await fetch(url, { ...options, headers });
      
      if (!res.ok) {
        const errText = await res.text();
        throw new Error(errText || `HTTP error ${res.status}`);
      }
      
      return res.json();
    } catch (err) {
      this.log(`ERROR: ${err.message}`);
      throw err;
    }
  },

  async loginUser(username, password) {
    try {
      this.log(`Login attempt: ${username}`);
      const body = new URLSearchParams();
      body.append('username', username);
      body.append('password', password);
      const res = await fetch(`${this.baseUrl}/token`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: body.toString(),
      });
      if (!res.ok) {
        const errText = await res.text();
        throw new Error(errText || 'Login failed');
      }
      return res.json();
    } catch (err) {
      this.log(`Login error: ${err.message}`);
      throw err;
    }
  },

  async registerUser(username, password) {
    try {
      this.log(`Registration attempt: ${username}`);
      const res = await fetch(`${this.baseUrl}/users/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password }),
      });

      if (!res.ok) {
        let errorMessage = 'Registration failed';
        try {
          const errorData = await res.json();
          if (errorData.detail) {
            errorMessage = errorData.detail;
          }
        } catch (e) {
          // If we can't parse JSON, try to get text
          const errorText = await res.text();
          if (errorText) {
            errorMessage = errorText;
          }
        }
        throw new Error(errorMessage);
      }

      return res.json();
    } catch (err) {
      this.log(`Registration error: ${err.message}`);
      throw err;
    }
  },

  async getWorkouts() {
    return this.fetchWithAuth(`${this.baseUrl}/workouts/`);
  },
  async getWorkout(id) {
    return this.fetchWithAuth(`${this.baseUrl}/workouts/${id}`);
  },
  async getSocialWorkout(id) {
    return this.fetchWithAuth(`${this.baseUrl}/workouts/social/${id}`);
  },
  async startWorkout() {
    return this.fetchWithAuth(`${this.baseUrl}/workouts/start/`, { method: 'POST' });
  },
  async completeWorkout(id, notes) {
    this.log(`Completing workout ${id}`);
    const payload = notes ? JSON.stringify({ description: notes }) : '{}';
    return this.fetchWithAuth(`${this.baseUrl}/workouts/${id}/complete`, {
      method: 'PUT',
      body: payload,
    });
  },
  async discardWorkout(id) {
    this.log(`Discarding workout ${id}`);
    return this.fetchWithAuth(`${this.baseUrl}/workouts/${id}/discard`, {
      method: 'DELETE',
    });
  },
  async getWorkoutInProgress() {
    try {
      return await this.fetchWithAuth(`${this.baseUrl}/workouts/in-progress/`);
    } catch {
      return null;
    }
  },
  async getWorkoutAnalysis(id) {
    try {
      this.log(`Fetching analysis for workout ${id}`);
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000);
      const data = await this.fetchWithAuth(
        `${this.baseUrl}/workouts/${id}/analysis`,
        { signal: controller.signal }
      );
      clearTimeout(timeoutId);
      return data;
    } catch (err) {
      this.log(`Analysis error: ${err.message}`);
      if (err.name === 'AbortError' || err.message.includes('404')) return null;
      throw err;
    }
  },

  async addExercise(workoutId, exerciseData) {
    // Make sure from_llm is set to false for user-selected exercises
    // unless explicitly specified
    const data = {
      ...exerciseData,
      from_llm: exerciseData.from_llm || false
    };

    return this.fetchWithAuth(`${this.baseUrl}/workouts/${workoutId}/exercises/`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },
  async deleteExercise(exerciseId) {
    return this.fetchWithAuth(`${this.baseUrl}/exercises/${exerciseId}`, {
      method: 'DELETE',
    });
  },
  async addSet(exerciseId, setData) {
    return this.fetchWithAuth(`${this.baseUrl}/exercises/${exerciseId}/sets/`, {
      method: 'POST',
      body: JSON.stringify(setData),
    });
  },
  async deleteSet(setId) {
    return this.fetchWithAuth(`${this.baseUrl}/sets/${setId}`, { method: 'DELETE' });
  },
  async deleteWorkout(id) {
    return this.fetchWithAuth(`${this.baseUrl}/workouts/${id}`, {
      method: 'DELETE',
    });
  },

  // Exercise Library endpoints
  async getExerciseLibrary(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.exercise_type) queryParams.append('exercise_type', params.exercise_type);
    if (params.muscle_group) queryParams.append('muscle_group', params.muscle_group);
    if (params.equipment) queryParams.append('equipment', params.equipment);
    if (params.search) queryParams.append('search', params.search);
    if (params.skip) queryParams.append('skip', params.skip);
    if (params.limit) queryParams.append('limit', params.limit);

    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
    return this.fetchWithAuth(`${this.baseUrl}/exercise-library/${queryString}`);
  },

  async getExercise(id) {
    return this.fetchWithAuth(`${this.baseUrl}/exercise-library/${id}`);
  },

  async getMuscleGroups() {
    return this.fetchWithAuth(`${this.baseUrl}/muscle-groups/`);
  },

  async getEquipment() {
    return this.fetchWithAuth(`${this.baseUrl}/equipment/`);
  },

  async getPersonalRecords() {
    try {
      this.log('Fetching personal records');
      return this.fetchWithAuth(`${this.baseUrl}/workouts/personal-records/`);
    } catch (err) {
      this.log(`Personal records error: ${err.message}`);
      throw err;
    }
  },

  // Scoring system endpoints
  async getUserStats() {
    try {
      this.log('Fetching user stats');
      return this.fetchWithAuth(`${this.baseUrl}/users/me/stats`);
    } catch (err) {
      this.log(`User stats error: ${err.message}`);
      throw err;
    }
  },

  async getUserMilestones() {
    try {
      this.log('Fetching user milestones');
      return this.fetchWithAuth(`${this.baseUrl}/users/me/milestones`);
    } catch (err) {
      this.log(`User milestones error: ${err.message}`);
      throw err;
    }
  },

  async getUserAchievements() {
    try {
      this.log('Fetching user achievements');
      return this.fetchWithAuth(`${this.baseUrl}/users/me/achievements`);
    } catch (err) {
      this.log(`User achievements error: ${err.message}`);
      throw err;
    }
  },

  async getAllAchievements() {
    try {
      this.log('Fetching all achievements');
      return this.fetchWithAuth(`${this.baseUrl}/achievements`);
    } catch (err) {
      this.log(`All achievements error: ${err.message}`);
      throw err;
    }
  },

  async getLeaderboard() {
    try {
      this.log('Fetching leaderboard');
      return this.fetchWithAuth(`${this.baseUrl}/leaderboard`);
    } catch (err) {
      this.log(`Leaderboard error: ${err.message}`);
      throw err;
    }
  },

  async getWorkoutScore(workoutId) {
    try {
      this.log(`Fetching score for workout ${workoutId}`);
      return this.fetchWithAuth(`${this.baseUrl}/workouts/${workoutId}/score`);
    } catch (err) {
      this.log(`Workout score error: ${err.message}`);
      throw err;
    }
  },

  async getExerciseMaxWeight(exerciseName) {
    try {
      this.log(`Fetching max weight for exercise: ${exerciseName}`);
      return this.fetchWithAuth(`${this.baseUrl}/exercises/max-weight/${encodeURIComponent(exerciseName)}`);
    } catch (err) {
      this.log(`Max weight fetch error: ${err.message}`);
      // Return a default object instead of throwing to avoid breaking the UI
      return {
        exercise_name: exerciseName,
        all_time_max: null,
        all_time_max_date: null,
        previous_max: null,
        previous_max_date: null,
        has_history: false
      };
    }
  },

  async getSocialFeed(days = 7, limit = 20) {
    try {
      this.log(`Fetching social feed (${days} days, limit ${limit})`);
      return this.fetchWithAuth(`${this.baseUrl}/workouts/social-feed/?days=${days}&limit=${limit}`);
    } catch (err) {
      this.log(`Social feed error: ${err.message}`);
      throw err;
    }
  },

  // Form Analysis endpoints
  async createFormAnalysis(exerciseId) {
    try {
      this.log(`Creating form analysis for exercise ${exerciseId}`);
      return this.fetchWithAuth(`${this.baseUrl}/form-analysis/`, {
        method: 'POST',
        body: JSON.stringify({ exercise_id: exerciseId }),
      });
    } catch (err) {
      this.log(`Form analysis creation error: ${err.message}`);
      throw err;
    }
  },

  async uploadFormVideo(formAnalysisId, formData) {
    try {
      this.log(`Uploading video for form analysis ${formAnalysisId}`);

      // Get the authentication token directly from localStorage
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found. Please log in again.');
      }

      // Make the request
      const res = await fetch(`${this.baseUrl}/form-analysis/${formAnalysisId}/upload`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      });

      // Handle response
      if (!res.ok) {
        let errorMessage = `HTTP error ${res.status}`;
        try {
          const errorText = await res.text();
          if (errorText) {
            errorMessage = errorText;
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
        }
        throw new Error(errorMessage);
      }

      // Parse and return the response
      const responseData = await res.json();
      this.log(`Video upload successful, received analysis results`);
      return responseData;
    } catch (err) {
      this.log(`Form video upload error: ${err.message}`);
      throw err;
    }
  },

  async getFormAnalysis(formAnalysisId) {
    try {
      this.log(`Fetching form analysis ${formAnalysisId}`);
      return this.fetchWithAuth(`${this.baseUrl}/form-analysis/${formAnalysisId}`);
    } catch (err) {
      this.log(`Form analysis fetch error: ${err.message}`);
      throw err;
    }
  },

  async getUserFormAnalyses() {
    try {
      this.log('Fetching user form analyses');
      return this.fetchWithAuth(`${this.baseUrl}/form-analysis/`);
    } catch (err) {
      this.log(`User form analyses fetch error: ${err.message}`);
      throw err;
    }
  },

  async getFormRules(exerciseId) {
    try {
      this.log(`Fetching form rules for exercise ${exerciseId}`);
      return this.fetchWithAuth(`${this.baseUrl}/form-rules/${exerciseId}`);
    } catch (err) {
      this.log(`Form rules fetch error: ${err.message}`);
      throw err;
    }
  },

  async getExerciseMuscleGroupMappings() {
    try {
      this.log('Fetching exercise-muscle group mappings');
      return this.fetchWithAuth(`${this.baseUrl}/exercise-muscle-mappings/`);
    } catch (err) {
      this.log(`Exercise-muscle group mappings fetch error: ${err.message}`);
      throw err;
    }
  },

  async getDiagnosticData() {
    try {
      this.log('Fetching diagnostic data for muscle groups');
      return this.fetchWithAuth(`${this.baseUrl}/diagnostic/exercise-muscle-mappings/`);
    } catch (err) {
      this.log(`Diagnostic data fetch error: ${err.message}`);
      throw err;
    }
  },

  // Weight tracking endpoints
  async addWeightEntry(weightData) {
    try {
      let logMessage = `Adding weight entry: ${weightData.weight}lbs`;
      if (weightData.body_fat !== null && weightData.body_fat !== undefined) {
        logMessage += `, body fat: ${weightData.body_fat}%`;
      }
      logMessage += ` on ${new Date(weightData.date).toLocaleDateString()}`;

      this.log(logMessage);
      return this.fetchWithAuth(`${this.baseUrl}/users/me/weight`, {
        method: 'POST',
        body: JSON.stringify(weightData),
      });
    } catch (err) {
      this.log(`Add weight entry error: ${err.message}`);
      throw err;
    }
  },

  async getWeightEntries(params = {}) {
    try {
      const queryParams = new URLSearchParams();

      if (params.skip) queryParams.append('skip', params.skip);
      if (params.limit) queryParams.append('limit', params.limit);
      if (params.start_date) queryParams.append('start_date', params.start_date.toISOString());
      if (params.end_date) queryParams.append('end_date', params.end_date.toISOString());

      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';

      this.log('Fetching weight entries');
      return this.fetchWithAuth(`${this.baseUrl}/users/me/weight${queryString}`);
    } catch (err) {
      this.log(`Get weight entries error: ${err.message}`);
      throw err;
    }
  },

  async updateWeightEntry(id, weightData) {
    try {
      let logMessage = `Updating weight entry ${id}: ${weightData.weight}lbs`;
      if (weightData.body_fat !== null && weightData.body_fat !== undefined) {
        logMessage += `, body fat: ${weightData.body_fat}%`;
      }

      this.log(logMessage);
      return this.fetchWithAuth(`${this.baseUrl}/users/me/weight/${id}`, {
        method: 'PUT',
        body: JSON.stringify(weightData),
      });
    } catch (err) {
      this.log(`Update weight entry error: ${err.message}`);
      throw err;
    }
  },

  async deleteWeightEntry(id) {
    try {
      this.log(`Deleting weight entry ${id}`);
      return this.fetchWithAuth(`${this.baseUrl}/users/me/weight/${id}`, {
        method: 'DELETE',
      });
    } catch (err) {
      this.log(`Delete weight entry error: ${err.message}`);
      throw err;
    }
  }
};


export default API_SERVICE;