// frontend/src/utils/timeUtils.js

/**
 * Format seconds into a human-readable time string
 * @param {number} seconds - Total seconds to format
 * @param {boolean} includeHours - Whether to include hours in the output
 * @returns {string} Formatted time string (e.g., "1:23:45" or "23:45")
 */
export const formatDuration = (seconds) => {
  if (!seconds && seconds !== 0) return '';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  // Format with hours if there are any or if includeHours is true
  if (hours > 0) {
    return `${hours}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
  }
  
  // Otherwise just show minutes:seconds
  return `${minutes}:${String(secs).padStart(2, '0')}`;
};

/**
 * Parse a time string into seconds
 * @param {string} timeString - Time string in format "HH:MM:SS" or "MM:SS"
 * @returns {number} Total seconds
 */
export const parseTimeToSeconds = (timeString) => {
  if (!timeString) return 0;
  
  const parts = timeString.split(':').map(part => parseInt(part, 10));
  
  if (parts.length === 3) {
    // HH:MM:SS format
    return parts[0] * 3600 + parts[1] * 60 + parts[2];
  } else if (parts.length === 2) {
    // MM:SS format
    return parts[0] * 60 + parts[1];
  } else if (parts.length === 1) {
    // SS format
    return parts[0];
  }
  
  return 0;
};
