{"name": "workout-tracker", "version": "0.1.0", "private": true, "dependencies": {"@mediapipe/pose": "^0.5.1675469404", "@tensorflow-models/pose-detection": "^2.1.3", "@tensorflow/tfjs": "^4.13.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.5.1", "axios": "^1.6.2", "chart.js": "^4.4.1", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^3.0.6", "formik": "^2.4.5", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-confetti": "^6.4.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "react-use": "^17.6.0", "react-webcam": "^7.2.0", "recharts": "^2.15.3", "web-vitals": "^3.5.0", "yup": "^1.3.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.4.0"}}