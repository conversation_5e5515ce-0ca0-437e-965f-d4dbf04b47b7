# BUILD STAGE
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files and install dependencies
COPY package.json package-lock.json ./
RUN npm install

# Copy environment variables
COPY .env.production ./

# Copy all source files
COPY . .

# Install tailwindcss and other dependencies if needed
RUN npm install --save-dev tailwindcss postcss autoprefixer @tailwindcss/forms

# Build the application
RUN npm run build

# PRODUCTION STAGE
FROM nginx:alpine

# Copy built files from builder stage
COPY --from=builder /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]