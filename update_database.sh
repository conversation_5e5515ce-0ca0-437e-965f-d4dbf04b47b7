#!/bin/bash

# Database update script for workout tracker

# Get environment variables
DB_HOST=${POSTGRES_HOST:-db}
DB_PORT=${POSTGRES_PORT:-5432}
DB_NAME=${POSTGRES_DB:-workout_db}
DB_USER=${POSTGRES_USER:-postgres}
DB_PASSWORD=${POSTGRES_PASSWORD:-postgres}

# SQL commands to update the database
SQL_COMMANDS=$(cat <<EOF
-- Check if status column exists in workouts table
DO \$\$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name='workouts' AND column_name='status'
    ) THEN
        -- Add status column to workouts table
        ALTER TABLE workouts ADD COLUMN status VARCHAR DEFAULT 'completed';
    END IF;
END
\$\$;

-- Create exercises table if it doesn't exist
CREATE TABLE IF NOT EXISTS exercises (
    id SERIAL PRIMARY KEY,
    name VARCHAR NOT NULL,
    exercise_type VARCHAR NOT NULL DEFAULT 'strength',
    workout_id INTEGER REFERENCES workouts(id) ON DELETE CASCADE
);

-- Create exercise_sets table if it doesn't exist
CREATE TABLE IF NOT EXISTS exercise_sets (
    id SERIAL PRIMARY KEY,
    reps INTEGER,
    weight FLOAT,
    duration_seconds INTEGER,
    distance FLOAT,
    notes TEXT,
    exercise_id INTEGER REFERENCES exercises(id) ON DELETE CASCADE
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_exercises_workout_id ON exercises(workout_id);
CREATE INDEX IF NOT EXISTS idx_exercise_sets_exercise_id ON exercise_sets(exercise_id);
CREATE INDEX IF NOT EXISTS idx_workouts_status ON workouts(status);
EOF
)

echo "Updating database schema for workout tracker..."
echo "Connecting to PostgreSQL at $DB_HOST:$DB_PORT"

# Execute SQL commands
if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "$SQL_COMMANDS"; then
    echo "Database schema updated successfully!"
else
    echo "Error updating database schema. Please check the PostgreSQL logs for details."
    exit 1
fi

echo "Done!"
