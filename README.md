# Workout Tracker Application

This is a full-stack application for tracking workouts built with React, FastAPI, and PostgreSQL, all containerized with Docker.

## Features

- User authentication and account management
- Add, edit, and delete workouts
- Record detailed workout information (exercises, sets, reps, weights)
- View workout history
- Responsive design with Tailwind CSS

## Technology Stack

- **Frontend**: React with Tailwind CSS
- **Backend**: Python FastAPI
- **Database**: PostgreSQL
- **Containerization**: Docker & Docker Compose

## Project Structure

```
workout-tracker/
├── docker-compose.yml      # Docker Compose configuration
├── frontend/               # React frontend application
│   ├── Dockerfile
│   ├── package.json
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── contexts/       # Context providers
│   │   ├── pages/          # Page components
│   │   ├── App.jsx         # Main application component
│   │   └── index.js        # Application entry point
│   └── ...
├── backend/                # FastAPI backend application
│   ├── Dockerfile
│   ├── main.py             # API endpoints
│   ├── models.py           # Database models
│   ├── database.py         # Database connection
│   ├── requirements.txt    # Python dependencies
│   └── ...
└── README.md
```

## Getting Started

### Prerequisites

- Docker and Docker Compose installed on your machine

### Installation

1. Clone this repository
```bash
git clone <repository-url>
cd workout-tracker
```

2. Start the application using Docker Compose
```bash
docker-compose up -d
```

3. Access the application
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000

### Usage

1. Register a new account
2. Log in with your credentials
3. Add your first workout
4. View, edit, or delete workouts as needed

## Development

To make changes to the application:

1. Edit the files as needed
2. Docker volumes are configured, so changes to the code will be reflected without rebuilding the containers in most cases
3. For dependency changes, you may need to rebuild the containers:
```bash
docker-compose up -d --build
```

## API Endpoints

- `/token` (POST) - Login and get access token
- `/users/` (POST) - Register a new user
- `/users/me/` (GET) - Get current user profile
- `/workouts/` (GET) - Get all workouts for current user
- `/workouts/` (POST) - Create a new workout
- `/workouts/{workout_id}` (GET) - Get a specific workout
- `/workouts/{workout_id}` (PUT) - Update a workout
- `/workouts/{workout_id}` (DELETE) - Delete a workout

## Security Notes

- For production use, update the SECRET_KEY in docker-compose.yml
- Consider using environment variables for sensitive configuration
- Add HTTPS for production deployments

## Future Enhancements

- Add exercise templates and presets
- Implement progress tracking and statistics
- Add social features (sharing workouts, following friends)
- Integrate with fitness tracking devices or APIs
- Add mobile app support